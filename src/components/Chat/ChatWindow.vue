<script lang="ts" setup>
  import { useApplicantProfile } from '@/components/ApplicantProfile/hooks/useApplicantProfile'
  import { useChatMessage } from '@/composables/ChatMessage/useChatMessage'
  import { useChatRoom } from '@/composables/ChatRoom/useChatRoom'
  import { useJobActions } from '@/composables/JobActions/useJobActions'
  import { usePusher } from '@/composables/usePusher'
  import { useSound } from '@/composables/useSound'
  import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
  import { useDisplay, useTheme } from 'vuetify'
  import { useChat } from './helpers/useChat'
  import { useChatStore } from '@/stores/apiStores/useChatStore'
  import { useResponsiveLeftSidebar } from '@core/composable/useResponsiveSidebar'
  import { avatarText } from '@core/utils/formatters'
  import avatar from '@images/avatars/avatar-0.png'
  import { useAppointments } from '@/composables/Appointments/useAppointments'

  defineProps({
    mini: {
      type: Boolean,
      default: false,
    },
  })

  const {
    state: { applicant },
  } = useApplicantProfile()

  const { play } = useSound()

  const vuetifyDisplays = useDisplay()
  const store = useChatStore()

  const { isLeftSidebarOpen } = useResponsiveLeftSidebar(
    vuetifyDisplays.smAndDown,
  )

  const { resolveAvatarBadgeVariant } = useChat()

  const {
    state: { isSettingJobAction },
    actions: { handleSetJobAction },
  } = useJobActions()

  const {
    actions: { saveChatMessage, initiateChatListen, stopChatListen },
  } = useChatMessage()

  const {
    state: { chatRoomDetails, loadingMatchChatRoom },
    actions: { fetchChatRoom },
  } = useChatRoom()

  const {
    state: { isFairAppointmentModule },
  } = useAppointments()

  const { listen } = usePusher()

  const startChat = async () => {
    try {
      const jobActionSet = await handleSetJobAction()
      if (jobActionSet.state === 'success') {
        await fetchChatRoom()
        await openChatOfContact()
      }
    } catch (e) {
      console.log('error::', e)
    }
  }

  const chatLogPS = ref()

  const scrollToBottomInChatLog = () => {
    if (chatLogPS.value) {
      const scrollEl = chatLogPS.value.$el || chatLogPS.value
      scrollEl.scrollTop = scrollEl.scrollHeight
    }
  }

  watch(chatRoomDetails, async data => {
    if (data?.id) {
      await openChatOfContact()
      await initiateChatListen(
        chatRoomDetails.value?.id,
        scrollToBottomInChatLog,
      )
    }
  })

  const q = ref('')

  watch(q, val => store.fetchChatsAndContacts(val), { immediate: true })

  const msg = ref('')

  const sendMessage = async () => {
    if (!msg.value) return
    const newMsg = msg.value
    console.log({ messageStored: msg.value })

    await store.sendMsg(msg.value)
    msg.value = ''
    await nextTick(() => {
      scrollToBottomInChatLog()
    })

    play('sendMsg')
    await saveChatMessage(newMsg)
  }

  const openChatOfContact = async () => {
    await store.getChat(chatRoomDetails.value)
    msg.value = ''
    if (vuetifyDisplays.smAndDown.value) isLeftSidebarOpen.value = false
    await nextTick(() => {
      scrollToBottomInChatLog()
    })
  }

  const showVideoWindow = () => {
    store.setShowChatWindow(false)
  }

  const isActiveChatUserProfileSidebarOpen = ref(false)

  const { name } = useTheme()

  const chatContentContainerBg = computed(() => 'white' || 'transparent')

  onMounted(async () => {
    await fetchChatRoom()
    if (chatRoomDetails.value?.id) {
      await openChatOfContact()
    }
  })

  onBeforeRouteLeave(() => {
    store.clearActiveChat()
    stopChatListen(chatRoomDetails.value?.id)
    nextTick()
  })
</script>

<template>
  <VLayout>
    <VMain>
      <v-card v-if="store.activeChat" class="d-flex flex-column h-100">
        <v-card
          class="active-chat-header d-flex align-center text-medium-emphasis bg-surface pa-2"
        >
          <IconBtn
            v-if="!mini"
            class="d-md-none me-3"
            @click="isLeftSidebarOpen = true"
          >
            <VIcon icon="tabler-menu-2" />
          </IconBtn>

          <div
            class="d-flex align-center cursor-pointer"
            @click="isActiveChatUserProfileSidebarOpen = true"
          >
            <VAvatar
              size="38"
              :variant="!store.activeChat?.contact?.avatar ? 'tonal' : 'text'"
              :color="
                !store.activeChat.contact.avatar
                  ? resolveAvatarBadgeVariant(store.activeChat.contact.status)
                  : undefined
              "
              class="cursor-pointer"
            >
              <VImg
                v-if="store.activeChat.contact.avatar"
                :src="applicant?.profileImageUrl || avatar"
                :alt="store.activeChat.contact.fullName"
              />
              <span v-else>{{
                avatarText(store.activeChat?.contact.fullName)
              }}</span>
            </VAvatar>
            <!--            </VBadge>-->

            <div class="flex-grow-1 ms-4 overflow-hidden">
              <p class="text-h6 mb-0">
                {{ applicant.firstName }} {{ applicant.lastName }}
              </p>
              <p class="text-truncate mb-0 text-disabled">
                {{ applicant.schoolName }}
              </p>
            </div>
          </div>

          <VSpacer />

          <!-- Header right content -->
          <div
            class="d-sm-flex align-center d-none"
            v-if="!isFairAppointmentModule"
          >
            <VBtn icon="tabler-video" @click="showVideoWindow" />
          </div>
        </v-card>

        <VDivider />

        <!-- Chat log -->
        <PerfectScrollbar
          ref="chatLogPS"
          tag="ul"
          :options="{ wheelPropagation: false }"
          class="flex-grow-1"
        >
          <ChatLog />
        </PerfectScrollbar>

        <!-- Message form -->
        <VForm class="chat-log-message-form mb-5 mx-5">
          <VTextField
            :key="store.activeChat?.contact.id"
            v-model="msg"
            variant="solo"
            class="chat-message-input"
            placeholder="Nachricht eingeben..."
            density="default"
            autofocus
            @keydown.enter.prevent="sendMessage"
          >
            <template #append-inner>
              <VBtn @click="sendMessage"> Senden </VBtn>
            </template>
          </VTextField>
        </VForm>
      </v-card>

      <!-- 👉 If not matched yet -->
      <v-card
        :loading="loadingMatchChatRoom"
        v-else
        class="d-flex h-100 align-center justify-center flex-column"
      >
        <VAvatar size="109" class="elevation-3 my-4 bg-surface">
          <VIcon
            size="50"
            class="rounded-0 text-high-emphasis"
            icon="tabler-message"
          />
        </VAvatar>
        <p class="mx-6 text-secondary" v-if="!loadingMatchChatRoom">
          Ist dieser Bewerber interessant für Sie? Dann matchen Sie Ihn und
          treten über den Chat oder einen Videocall in Verbindung
        </p>
        <v-btn
          variant="outlined"
          v-if="isFairAppointmentModule"
          class="mb-6 px-6 py-1 font-weight-medium text-lg elevation-3 rounded-xl text-high-emphasis"
          :class="[{ 'cursor-pointer': $vuetify.display.smAndDown }]"
          :disabled="loadingMatchChatRoom"
          :loading="loadingMatchChatRoom"
          @click="startChat"
        >
          Start Chat
        </v-btn>
        <v-btn
          v-else
          class="mb-6 px-6 py-1 font-weight-medium text-lg elevation-3 rounded-xl text-high-emphasis bg-primary"
          :class="[{ 'cursor-pointer': $vuetify.display.smAndDown }]"
          :disabled="isSettingJobAction || loadingMatchChatRoom"
          :loading="isSettingJobAction || loadingMatchChatRoom"
          @click="startChat"
        >
          Bewerber matchen
        </v-btn>
      </v-card>
    </VMain>
  </VLayout>
</template>

<style lang="scss">
  @use '@styles/variables/_vuetify.scss';
  @use '@core/scss/base/_mixins.scss';
  @use '@layouts/styles/mixins' as layoutsMixins;

  // Variables
  $chat-app-header-height: 62px;

  // Placeholders
  %chat-header {
    display: flex;
    align-items: center;
    min-block-size: $chat-app-header-height;
    padding-inline: 1rem;
  }

  .chat-app-layout {
    border-radius: vuetify.$card-border-radius;

    //@include mixins.elevation(vuetify.$card-elevation);

    $sel-chat-app-layout: &;

    @at-root {
      .skin--bordered {
        @include mixins.bordered-skin($sel-chat-app-layout);
      }
    }

    .active-chat-user-profile-sidebar,
    .user-profile-sidebar {
      .v-navigation-drawer__content {
        display: flex;
        flex-direction: column;
      }
    }

    .chat-list-header,
    .active-chat-header {
      @extend %chat-header;
    }

    .chat-list-search {
      .v-field__outline__start {
        flex-basis: 20px !important;
        border-radius: 28px 0 0 28px !important;
      }

      .v-field__outline__end {
        border-radius: 0 28px 28px 0 !important;
      }

      @include layoutsMixins.rtl {
        .v-field__outline__start {
          flex-basis: 20px !important;
          border-radius: 0 28px 28px 0 !important;
        }

        .v-field__outline__end {
          border-radius: 28px 0 0 28px !important;
        }
      }
    }

    .chat-list-sidebar {
      .v-navigation-drawer__content {
        display: flex;
        flex-direction: column;
      }
    }
  }

  .chat-content-container {
    /* stylelint-disable-next-line value-keyword-case */
    background-color: v-bind(chatContentContainerBg);

    // Adjust the padding so text field height stays 48px
    .chat-message-input {
      .v-field__append-inner {
        align-items: center;
        padding-block-start: 0;
      }

      .v-field--appended {
        padding-inline-end: 9px;
      }
    }
  }

  .chat-user-profile-badge {
    .v-badge__badge {
      /* stylelint-disable liberty/use-logical-spec */
      min-width: 12px !important;
      height: 0.75rem;
      /* stylelint-enable liberty/use-logical-spec */
    }
  }
</style>
