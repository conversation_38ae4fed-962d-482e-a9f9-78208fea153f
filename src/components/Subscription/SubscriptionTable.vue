<script setup lang="ts">
  import MoreBtn from '@/components/MoreBtn.vue'
  import { SubscriptionHeaders } from '@/composables/StripeSubscription/tableHeaders'
  import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
  import { paginationMeta } from '@/utils/utils'
  import AppSelect from '@core/components/AppSelect.vue'
  import { useTheme } from 'vuetify'

  const searchQuery = ref('')
  const selectedStatus = ref()

  const {
    state: { subscriptionList, loadingSubscription, loadingPortalSession },
    actions: {
      handleCancelSubscription,
      handleResumeSubscription,
      handleRowClicked,
      getPortalSession,
      copyInvoiceLink,
      handleGenerateInvoice,
      handleDownloadInvoice,
      loadSubscription,
    },
  } = useStripeSubscription()

  onMounted(async () => {
    await loadSubscription()
  })

  const isDarkLayout = useTheme().global.current

  const itemsPerPage = ref(10)
  const page = ref(1)

  const totalAds: Ref<number> = computed(() =>
    subscriptionList.value ? (subscriptionList.value as any[]).length : 0,
  )

  const paginatedSubscriptions = computed(() => {
    const start = (page.value - 1) * itemsPerPage.value
    const end = start + itemsPerPage.value
    return subscriptionList.value.slice(start, end)
  })

  const computedMoreList = computed(() => {
    return (raw: { invoiceId: string }) => [
      {
        title: 'View Invoice',
        value: 'pause',
        prependIcon: 'mdi-play',
        action: () => handleGenerateInvoice(raw.invoiceId),
      },
      {
        title: 'Download PDF',
        value: 'pdf',
        prependIcon: 'tabler-file-type-pdf',
        action: () => handleDownloadInvoice(raw.invoiceId),
      },
      {
        title: 'Copy Invoice Link',
        value: 'link',
        prependIcon: 'tabler-link',
        action: () => copyInvoiceLink(raw.invoiceId),
      },
      1,
    ]
  })

  const handleMoreAction = (item: any) => {
    if (item && item.action) {
      item.action()
    }
  }

  const formatDate = (date: string): string => {
    const options: Intl.DateTimeFormatOptions = {
      day: 'numeric',
      month: 'numeric',
      year: 'numeric',
    }
    const dateObj = new Date(date)
    return dateObj.toLocaleDateString('de', options)
  }
</script>

<template>
  <VCard elevation="20" :title="$t('Subscription Management')">
    <template v-slot:append>
      <v-btn
        size="small"
        :disabled="loadingPortalSession"
        :loading="loadingPortalSession"
        @click="getPortalSession"
      >
        Zum Rechnungsportal
      </v-btn>
    </template>
    <VCardText>
      <div class="d-flex align-end flex-wrap gap-3">
        <AppTextField
          v-model="searchQuery"
          placeholder="Subscription suchen"
          density="compact"
          prepend-inner-icon="mdi-magnify"
          class="me-3"
        />
        <div class="invoice-list-status">
          <AppSelect
            v-model="selectedStatus"
            density="compact"
            placeholder="Filter Status"
            clearable
            clear-icon="tabler-x"
            :items="['Active', 'Cancelled']"
            style="inline-size: 12rem"
          />
        </div>
      </div>
    </VCardText>
    <VDivider />

    <VDataTable
      :headers="SubscriptionHeaders"
      :items="paginatedSubscriptions"
      :loading="loadingSubscription"
      item-value="id"
      class="px-2"
      :search="searchQuery"
      hover
      @click:row="(event, value) => handleRowClicked(event, value.item)"
    >
      <template #no-data>
        <div>Keine Daten vorhanden</div>
      </template>
      <template #[`item.status`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip v-if="item.isActive" color="success" text="Aktiv" label />
          <VChip v-else color="error" text="Gekündigt" label />
          <VChip
            v-if="item.cancelAtPeriodEnd && item.isActive"
            :text="`Läuft aus am ${formatDate(item.expiresAt)} `"
            color="grey-darken-4 text-error"
            append-icon="mdi-access-time"
            label
          />
          <VChip
            v-if="item.isActive && !item.cancelAtPeriodEnd"
            :text="`Verlängerung ${formatDate(item.expiresAt)} `"
            color="success"
            append-icon="mdi-access-time"
            label
          />
        </div>
      </template>
      <!-- END status column. -->

      <template #[`item.subscription`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip
            prepend-icon="mdi-circle"
            v-if="item.subscription === 'BASIC'"
            color="primary"
            :text="item.subscription"
          />
          <VChip
            prepend-icon="mdi-crown"
            v-else-if="item.subscription === 'PREMIUM'"
            color="gold"
            :text="`${item.subscription}`"
          />
        </div>
      </template>

      <template #[`item.amountTotal`]="{ item }">
        <div class="d-flex flex-wrap chip-wrapper">
          <VChip v-if="item.percent_off" prepend-icon="mdi-currency-eur">
            <s class="text-caption text-primary mr-2"
              >{{ item.amountTotal / 100 }}
            </s>
            <b>{{
              item.amountTotal / 100 -
              (item.amountTotal / 100 / 100) * item.percent_off
            }}</b>
          </VChip>
          <VChip prepend-icon="mdi-currency-eur" v-else>
            <b>{{ item.amountTotal / 100 }}</b>
          </VChip>
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <IconBtn
          v-if="item.cancelAtPeriodEnd"
          class="mx-2 text-none"
          color="success"
          variant="flat"
          @click="
            handleResumeSubscription(
              item.stripeSubscriptionId,
              isDarkLayout.dark,
            )
          "
          size="x-small"
          icon="mdi-refresh"
        >
          <VIcon icon="mdi-refresh" />
          <v-tooltip activator="parent" location="top"
            >Renew Subscription
          </v-tooltip>
        </IconBtn>

        <IconBtn
          v-else
          class="mx-2"
          color="primary"
          @click="
            handleCancelSubscription(
              item.stripeSubscriptionId,
              isDarkLayout.dark,
            )
          "
          size="x-small"
          variant="flat"
          icon="mdi-cancel"
        >
          <VIcon icon="mdi-cancel" />
          <v-tooltip activator="parent" location="top"
            >Cancel Subscription
          </v-tooltip>
        </IconBtn>
        <MoreBtn
          more-icon="mdi-cancel"
          :menu-list="computedMoreList(item)"
          item-props
          color="undefined"
          @item-clicked="val => handleMoreAction(val)"
        />
      </template>

      <template #bottom>
        <VDivider />
        <div
          class="d-flex align-center justify-sm-space-between justify-center flex-wrap gap-3 pa-5 pt-3"
        >
          <p class="text-sm text-disabled mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalAds as number) }}
          </p>

          <VPagination
            v-model="page"
            :length="Math.ceil(totalAds / itemsPerPage)"
            :total-visible="
              $vuetify.display.xs ? 1 : Math.ceil(totalAds / itemsPerPage)
            "
          >
            <template #prev="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Zurück
              </VBtn>
            </template>

            <template #next="slotProps">
              <VBtn
                variant="tonal"
                color="default"
                v-bind="slotProps"
                :icon="false"
              >
                Weiter
              </VBtn>
            </template>
          </VPagination>
        </div>
      </template>
    </VDataTable>

    <VDivider />
  </VCard>
</template>

<style lang="scss">
  #invoice-list {
    .invoice-list-actions {
      inline-size: 8rem;
    }

    .invoice-list-search {
      inline-size: 12rem;
    }
  }
</style>
