<script setup lang="ts">
  import { useUsersStore } from '@/stores/usersStore'
  import { computed, reactive } from 'vue'
  import { VForm } from 'vuetify/lib/components/VForm/index'
  import { emailValidator, requiredValidator } from '@validators'
  import { useAuthStore } from '@/stores/authStore'
  import type ImageCropperResult from '@/types/image-cropper-result'

  const props = defineProps({
    isDrawerOpen: {
      type: Boolean,
      required: true,
    },
    user: {
      type: Object,
      required: false,
      default: null,
    },
    loading: {
      type: Boolean,
      required: false,
      default: false,
    },
  })

  const formRef = ref<InstanceType<typeof VForm>>()

  const authStore = useAuthStore()
  const userStore = useUsersStore()
  const currentUserId = computed(() => authStore.user?.uid)

  const emit = defineEmits([
    'update:isDrawerOpen',
    'userData',
    'clearUser',
    'deleteUser',
  ])

  const name = ref('')
  const imageUrl = ref(null)
  const imageResult = ref<ImageCropperResult>()
  const email = ref('')

  const rights = reactive({
    createJobAd: false,
    createUser: false,
    editCompany: false,
    superAdmin: false,
  })

  const superAdminRight = computed({
    get() {
      return userStore.superAdmin
    },
    set(val) {
      userStore.updateSuperAdminRight(val)
    },
  })

  const createJobAdRight = computed({
    get() {
      return userStore.createJobAd
    },
    set(val) {
      userStore.updateCreateJobAdRight(val)
    },
  })

  const createUserRight = computed({
    get() {
      return userStore.createUser
    },
    set(val) {
      userStore.updateCreateUserRight(val)
    },
  })

  const editCompanyRight = computed({
    get() {
      return userStore.editCompany
    },
    set(val) {
      userStore.updateEditCompanyRight(val)
    },
  })

  const handleDrawerModelValueUpdate = (val: boolean) => {
    emit('update:isDrawerOpen', val)
    if (!val) emit('clearUser')
    formRef.value?.reset()
  }

  const closeNavigationDrawer = () => {
    emit('update:isDrawerOpen', false)
    emit('clearUser')
    formRef.value?.reset()
  }

  const deleteUser = () => {
    emit('deleteUser', props.user.id)
    emit('update:isDrawerOpen', false)
  }

  const submitForm = async () => {
    if (formRef.value) {
      const { valid } = await formRef.value?.validate()
      if (valid) {
        emit('userData', {
          imageResult: imageResult.value,
          rights,
        })
        emit('update:isDrawerOpen', false)
      }
    }
  }
</script>

<template>
  <VNavigationDrawer
    :width="400"
    location="end"
    :model-value="props.isDrawerOpen"
    temporary
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Title -->
    <div class="d-flex align-center pa-6 pb-1">
      <div class="v-card-title pl-0">
        {{ props.user?.id != null ? 'Bearbeiten' : 'Benutzer anlegen' }}
      </div>
      <VSpacer />

      <!-- 👉 Close btn -->
      <VBtn
        v-if="props.user.id != null"
        color="primary"
        variant="tonal"
        prepend-icon="tabler-trash"
        class="rounded"
        @click="deleteUser"
      >
        Löschen
      </VBtn>
    </div>

    <VCard flat class="h-100">
      <VCardText>
        <VForm ref="formRef" validate-on="submit">
          <VRow>
            <VCol cols="12">
              <ImageInputCrop
                :aspect-ratio="1"
                title="Profilbild"
                preview-height="200px"
                :image-url="userStore.userForm.avatarImageUrl || imageUrl"
                @image-cropped="result => (imageResult = result)"
              />
            </VCol>
            <VCol cols="12">
              <VTextField
                v-model="userStore.userForm.name"
                label="Name"
                :rules="[requiredValidator]"
              />
            </VCol>
            <VCol cols="12">
              <VTextField
                v-model="userStore.userForm.email"
                label="Email"
                :rules="[requiredValidator, emailValidator]"
              />
            </VCol>
            <VCol cols="12">
              <VSwitch v-model="superAdminRight" label="Administrator" />
              <VSwitch
                v-model="createJobAdRight"
                label="Anzeigen erstellen"
                :disabled="rights.superAdmin || currentUserId === props.user.id"
              />
              <VSwitch
                v-model="createUserRight"
                label="Benutzer verwalten"
                :disabled="rights.superAdmin || currentUserId === props.user.id"
              />
              <VSwitch
                v-model="editCompanyRight"
                label="Unternehmen bearbeiten"
                :disabled="rights.superAdmin || currentUserId === props.user.id"
              />
            </VCol>
            <VSpacer />
            <VCol cols="12" class="d-flex justify-space-between mt-2">
              <VBtn color="primary" @click="closeNavigationDrawer">
                Abbrechen
              </VBtn>
              <VBtn color="success" @click="submitForm" :loading="loading">
                Speichern
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VNavigationDrawer>
</template>
