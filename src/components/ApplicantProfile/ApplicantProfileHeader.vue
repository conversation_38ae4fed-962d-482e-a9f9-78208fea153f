<script lang="ts" setup>
  import { computed, ref } from 'vue'
  import { useJobActions } from '@/composables/JobActions/useJobActions'
  import { useJobAdsStore } from '@/stores/jobAdsStore'
  import avatar from '@images/avatars/avatar-0.png'
  import { timestampToAge } from '@/utils/utils'
  import { useApplicantProfile } from '@/components/ApplicantProfile/hooks/useApplicantProfile'
  import { useJobLikes } from '@/composables/JobAdverts/useJobLikes'
  import { useRouter, useRoute } from 'vue-router'
  import { useFilterStore } from '@/stores/fairs/filterStore'
  import { useAppointments } from '@/composables/Appointments/useAppointments'

  // Composables and stores
  const jobAdsStore = useJobAdsStore()
  const filterStore = useFilterStore()
  const route = useRoute()
  const router = useRouter()

  // Component state
  const {
    state: { statusArray, applicant, loadingApplicant },
  } = useApplicantProfile()

  const {
    state: { jobAdvertTitle },
  } = useJobLikes()

  const {
    state: { isSettingJobAction },
    actions: { handleUpdateStatus },
  } = useJobActions()

  const {
    state: { isFairAppointmentModule },
  } = useAppointments()

  // Reactive data
  const jobAdvertId = route.params.id as string
  const applicantId = route.params.uid as string

  const appointmentDetails = computed(() => {
    return isFairAppointmentModule ? filterStore.activeAppointment || {} : {}
  })

  const jobAdLike = computed(() => {
    return jobAdsStore.jobAdLikes[jobAdvertId]?.find(
      like => like.applicantId === applicantId,
    )
  })

  // Fully reactive status from store
  const status = computed(() => jobAdLike.value?.status || '...')

  // VSelect controlled status
  const updatedStatus = computed({
    get: () => status.value,
    set: (newStatus: string) => handleStatusChange(newStatus),
  })

  // Status change handler
  async function handleStatusChange(newStatus: string) {
    if (!jobAdLike.value || newStatus === status.value) return

    try {
      await handleUpdateStatus(newStatus)

      // Update URL query
      await router.push({
        query: {
          ...route.query,
          status: newStatus,
        },
      })
    } catch (error) {
      console.error('Failed to update status:', error)
    }
  }
</script>

<template>
  <v-skeleton-loader
    v-if="loadingApplicant"
    class="mx-auto border py-6"
    type="list-item-avatar-three-line"
  ></v-skeleton-loader>

  <VCard v-else>
    <VCardText
      class="d-flex align-bottom flex-sm-row flex-column justify-center gap-x-5"
    >
      <!-- Avatar Section -->
      <div class="d-flex">
        <VAvatar
          size="100"
          :image="applicant?.profileImageUrl || avatar"
          class="mx-auto my-auto"
        />
      </div>

      <!-- Profile Info Section -->
      <div class="w-100 mt-8 pt-4 mt-sm-0">
        <h6 class="text-h6 text-center text-sm-start font-weight-medium mb-3">
          {{ applicant?.firstName }} {{ applicant?.lastName }}
          <span class="font-weight-bold ml-4" v-if="!isFairAppointmentModule">
            ({{ jobAdvertTitle }})
          </span>
          <span>
            <VChip
              v-if="isSettingJobAction"
              class="ml-4"
              color="success"
              label
              text-color="white"
            >
              <v-progress-circular size="14" width="2" indeterminate />
            </VChip>
            <v-template v-else>
              <VChip
                v-if="isFairAppointmentModule"
                class="ml-4"
                :color="
                  appointmentDetails?.status === 'confirmed'
                    ? 'success'
                    : 'error'
                "
                label
                text-color="white"
              >
                <VIcon start icon="tabler-heart-handshake" />
                {{
                  isFairAppointmentModule
                    ? appointmentDetails?.status?.toUpperCase()
                    : status
                }}
              </VChip>
              <VChip
                v-else
                class="ml-4"
                :color="status === 'MATCHED' ? 'success' : 'error'"
                label
                text-color="white"
              >
                <VIcon start icon="tabler-heart-handshake" />
                {{
                  isFairAppointmentModule
                    ? appointmentDetails?.status?.toUpperCase()
                    : status
                }}
              </VChip>
            </v-template>
          </span>
        </h6>

        <!-- Profile Details -->
        <div
          class="d-flex align-center justify-center justify-sm-space-between flex-wrap gap-4"
        >
          <div
            class="d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-4"
          >
            <!-- Phone -->
            <span class="d-flex" v-if="applicant?.phoneNumber">
              <VIcon size="20" icon="tabler-phone" class="me-1" />
              <span class="text-body-1">{{ applicant.phoneNumber }}</span>
            </span>

            <!-- Age -->
            <span class="d-flex">
              <VIcon size="20" icon="tabler-calendar-event" class="me-1" />
              <span class="text-body-1">{{
                timestampToAge(applicant?.birthDate)
              }}</span>
            </span>

            <!-- City -->
            <span v-if="applicant?.city" class="d-flex">
              <VIcon size="20" icon="tabler-map-pin" class="me-1" />
              <span class="text-body-1">{{ applicant.city }}</span>
            </span>

            <!-- Appointment/Availability -->
            <template v-if="isFairAppointmentModule">
              <span class="d-flex">
                <VIcon size="20" icon="tabler-calendar" class="me-1" />
                <span>{{ $t('Appointment Slot') }}:</span>
                <VChip
                  v-if="appointmentDetails?.date"
                  class="ml-2 font-weight-bold success"
                  label
                  text-color="white"
                >
                  <v-icon class="mr-2">mdi-calendar</v-icon>
                  {{ appointmentDetails.date }}
                </VChip>
                <VChip
                  v-if="appointmentDetails?.startTime"
                  class="ml-2 font-weight-bold success"
                  color="success"
                  label
                  text-color="white"
                >
                  <v-icon class="mr-2">mdi-clock</v-icon>
                  {{ appointmentDetails.startTime }} -
                  {{ appointmentDetails.endTime }}
                </VChip>
                <!-- Time chips... -->
              </span>
            </template>
            <template v-else>
              <span class="d-flex">
                <VIcon size="20" icon="tabler-calendar" class="me-1" />
                <span>{{ $t('Available From') }}:</span>
                <VChip
                  class="ml-2 font-weight-bold success"
                  label
                  color="success"
                  text-color="white"
                >
                  {{
                    applicant?.availableFrom != null
                      ? new Date(applicant.availableFrom).toLocaleDateString()
                      : ''
                  }}
                </VChip>
              </span>
            </template>
          </div>

          <!-- Status Selector -->
          <VRow v-if="!isFairAppointmentModule">
            <VCol cols="8" offset="4">
              <VSelect
                v-model="updatedStatus"
                :label="$t('Change Status')"
                :items="statusArray"
                variant="solo"
                :disabled="isSettingJobAction"
              />
            </VCol>
          </VRow>
        </div>
      </div>
    </VCardText>
    <v-card
      v-if="isFairAppointmentModule && appointmentDetails?.rejectReason"
      class="mx-auto"
      style="border-radius: 0px"
      flat
      elevation="0"
    >
      <v-card-text class="bg-grey-200 pt-4">
        {{
          appointmentDetails.rejectReason ? appointmentDetails.rejectReason : ''
        }}
      </v-card-text>
    </v-card>
  </VCard>
</template>

<style lang="scss">
  .user-profile-avatar {
    border: 5px solid rgb(var(--v-theme-surface));
    background-color: rgb(var(--v-theme-surface)) !important;
    inset-block-start: -3rem;
  }
</style>
