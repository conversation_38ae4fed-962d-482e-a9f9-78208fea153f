<script setup lang="ts">
  import { usePermissions } from '@/composables/Permissions/usePermissions'
  import router from '@/router'
  import { useAppStore } from '@/stores/appStore'
  import { useCompanyFormStore } from '@/stores/company-form/companyFormStore'
  import PlacesAutocomplete from '@/components/PlacesAutocomplete.vue'
  import { requiredValidator } from '@validators'
  import { useTheme } from 'vuetify'
  import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useCompanyDetailsGraphData } from '@/api/graphHooks/company/useCompanyDetailsGraphData'
  import { useAuthGraph } from '@/api/graphHooks/useAuthGraph'
  import { useStaleImage } from '@/composables/StaleImage/useStaleImage'
  import { newCompanySaveSuccess } from '@/composables/useSweetAlert'
  import { useAuthStore } from '@/stores/authStore'
  import { deleteTextImage } from '@/libs/firebase/upload-text-image'

  const props = defineProps({
    isInitialSetup: {
      type: Boolean,
      required: false,
      default: false,
    },
    isByAdmin: {
      type: Boolean,
      required: false,
      default: false,
    },
  })

  const {
    state: { saveCompanyLoading, updateCompanyLoading },
    actions: { saveCompany, saveCompanyByAdmin, updateCompany },
  } = useCompanyGraph()

  const {
    actions: { handleRemoveMultipleStaleImages },
  } = useStaleImage()

  const {
    actions: { setCustomClaims },
  } = useAuthGraph()

  const authStore = useAuthStore()

  const {
    state: { companyDetails },
    actions: { refetchCompany },
  } = useCompanyDetailsGraphData()

  const {
    state: { permissions },
  } = usePermissions()

  const formRef = ref()
  const loading = computed(() => {
    return props.isInitialSetup
      ? saveCompanyLoading.value
      : updateCompanyLoading.value
  })

  const editorRef = ref()

  const formStore = useCompanyFormStore()
  const companyStore = useCompanyStore()
  const appStore = useAppStore()

  const isDarkLayout = useTheme().global.current

  const genLoading = ref(false)

  const initializeForm = async () => {
    if (!props.isInitialSetup) {
      try {
      } catch (error) {
        console.error('Error loading company data:', error)
      }
    }

    const company = computed(() => {
      return companyStore.company || companyDetails.value
    })

    formStore.init(props.isInitialSetup, company.value)
  }

  const resetForm = () => {
    formRef.value?.reset()
    editorRef.value?.resetEditor()
    formStore.resetStore()
  }

  onMounted(async () => {
    await initializeForm()
  })

  const submitForm = async () => {
    const isValid = (await formRef.value?.validate())?.valid
    if (!isValid) {
      appStore.showSnack('Bitte füllen Sie alle Felder aus')
      return
    }

    genLoading.value = true

    try {
      if (props.isInitialSetup && !props.isByAdmin) {
        await handleInitialSetup()
      } else if (props.isByAdmin) {
        await handleAdminCreate()
      } else {
        await handleUpdate()
      }
    } catch (error) {
      console.error('Form submission failed:', error)
      appStore.showSnack('Ein Fehler ist aufgetreten')
    } finally {
      genLoading.value = false
    }
  }

  const handleInitialSetup = async () => {
    try {
      await handleRemoveMultipleStaleImages()
      await companyStore.uploadCompanyImages(formStore)
      const newCompany = await saveCompany()
      companyStore.updateCompany(newCompany?.data.createCompany)
      await setCustomClaims()

      const doneAlert = await newCompanySaveSuccess(isDarkLayout.value.dark)
      if (doneAlert?.isConfirmed) {
        await authStore.init()
      }
      await router.push({ name: 'company' })
      resetForm()
    } catch (e) {
      console.log(e)
      appStore.showSnack('Ein Fehler ist aufgetreten')
    }
  }

  const handleAdminCreate = async () => {
    try {
      await handleRemoveMultipleStaleImages()
      await companyStore.uploadCompanyImages(formStore)
      const newCompany = await saveCompanyByAdmin()
      companyStore.updateCompany(newCompany?.data.createCompany)

      const doneAlert = await newCompanySaveSuccess(isDarkLayout.value.dark)
      if (doneAlert?.isConfirmed) {
        await authStore.init()
      }
      appStore.showSnack('Unternehmen erfolgreich hinzugefügt')
      resetForm()
      companyStore.toggleCompanyForm()
    } catch (e) {
      console.log(e)
      appStore.showSnack('Ein Fehler ist aufgetreten')
    }
  }

  const handleUpdate = async () => {
    try {
      const userDetailsString = localStorage.getItem('userDetails')
      const userDetails = userDetailsString ? JSON.parse(userDetailsString) : null
      const companyUserId = authStore?.claims?.companyUserId || userDetails?.companyUserId
      const imagesToDelete = formStore?.imagesToDelete

      //check if a new image was uploaded
      const logoChangedUrl = formStore?.logoResult?.imageUrl
      const headerChangedUrl = formStore?.headerResult?.imageUrl

      if (logoChangedUrl || headerChangedUrl) {
        await companyStore.uploadCompanyImages(formStore)
      }

      const variables = {
        id: companyStore.getCompany?.id,
        companyInput: {
          companyUserId: companyUserId,
          name: formStore.companyName,
          city: formStore.city,
          country: formStore.address?.split(',').pop(),
          address: formStore.address,
          detailContent: formStore.detailContent,
          foundingYear: formStore?.date,
          headerImageUrl: formStore?.headerSavedUrl,
          logoImageUrl: formStore?.logoSavedUrl,
          latitude: formStore?.position?.lat,
          longitude: formStore?.position?.long,
          totalEmployees: formStore.mitarbeiter,
        },
      }

      const updatedCompany = await updateCompany(variables)
      await handleRemoveMultipleStaleImages()

      for (const url of imagesToDelete) {
        await deleteTextImage(url)
      }

      appStore.showSnack('Unternehmensdaten erfolgreich aktualisiert')
      resetForm()
    } catch (e) {
      console.log(e)
      appStore.showSnack('Ein Fehler ist aufgetreten')
    }
  }

  //refetch company details when mounted
  onMounted(() => {
    if (!props.isInitialSetup) {
      refetchCompany()
    }
  })
</script>

<template>
  <VForm
    ref="formRef"
    :disabled="
      (!(permissions.isSuperAdmin || permissions.canEditCompany) &&
        !isInitialSetup) ||
      loading ||
      genLoading
    "
    @submit.prevent="submitForm"
  >
    <VRow>
      <VCol :cols="isByAdmin ? 12 : 6">
        <VRow>
          <VCol cols="12">
            <VTextField
              v-model="formStore.companyName"
              label="Unternehmensname"
              :rules="[requiredValidator]"
            />
          </VCol>
          <VCol cols="12">
            <PlacesAutocomplete
              v-if="
                (formStore.address && formStore.address.length > 0) ||
                props.isInitialSetup
              "
              label="Adresse des Unternehmens"
              :address="formStore.address"
              @place-changed="formStore.placeChanged"
            />
          </VCol>
          <VCol cols="6">
            <VTextField
              v-model.number="formStore.mitarbeiter"
              label="Anzahl der Mitarbeiter"
              :rules="[requiredValidator]"
            />
          </VCol>
          <VCol cols="6">
            <VTextField
              v-model.number="formStore.date"
              label="Gründungsjahr"
              :rules="[requiredValidator]"
            />
          </VCol>
          <VCol :cols="12" class="d-flex align-end flex-wrap gap-5">
            <!--            <ImageInputCrop-->
            <!--              v-if="props.isInitialSetup"-->
            <!--              :class="{ 'w-100': formStore.avatarResult == null }"-->
            <!--              :aspect-ratio="1"-->
            <!--              preview-height="160px"-->
            <!--              title="Profilbild"-->
            <!--              @image-cropped="-->
            <!--                result => formStore.imageChanged(result, 'profile')-->
            <!--              "-->
            <!--            />-->

            <ImageInputCrop
              :class="{
                'w-100':
                  formStore.logoResult == null &&
                  formStore.logoSavedUrl == null,
              }"
              :min-aspect-ratio="1"
              :max-aspect-ratio="3"
              preview-height="160px"
              :image-url="formStore.logoSavedUrl"
              title="Logo"
              @image-cropped="result => formStore.imageChanged(result, 'logo')"
            />
            <ImageInputCrop
              :class="{
                'w-100':
                  formStore.headerResult == null &&
                  formStore.headerSavedUrl == null,
              }"
              :aspect-ratio="1.75"
              title="Titelbild"
              preview-height="160px"
              :image-url="formStore.headerSavedUrl"
              @image-cropped="
                result => formStore.imageChanged(result, 'header')
              "
            />
          </VCol>
        </VRow>
      </VCol>
      <VCol :cols="isByAdmin ? 12 : 6">
        <TextEditor
          ref="editorRef"
          v-if="formStore.companyName || isInitialSetup"
          v-model="formStore.detailContent"
          max-height="450px"
          @update:stale-img-ids="
            staleImgIds => (formStore.staleImgIds = staleImgIds)
          "
          @update:images-to-delete="
            imagesToDelete => (formStore.imagesToDelete = imagesToDelete)
          "
        />
      </VCol>

      <VCol cols="12">
        <VBtn
          type="submit"
          color="success"
          :loading="loading || genLoading"
          :disabled="loading || genLoading"
        >
          <template v-slot:loader>
            <v-progress-circular
              size="20"
              :width="2"
              indeterminate
              color="white"
            ></v-progress-circular>
          </template>
          {{ isInitialSetup ? 'Unternehmen anlegen' : 'Änderungen speichern' }}
        </VBtn>
      </VCol>
    </VRow>
  </VForm>
</template>
