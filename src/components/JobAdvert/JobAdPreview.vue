<script setup lang="ts">
  import { useAuthStore } from '@/stores/authStore'
  import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import { useCompanyStore } from '@/stores/companyStore'
  import iphone from '@/assets/iphoneFrame.png'
  import {
    confirmEditJobAd,
    jobEditSuccess,
    jobSaveSuccess,
  } from '@/composables/useSweetAlert'
  import { useSubscriptionStore } from '@/stores/subscription/subscriptionStore'
  import { formatDateToString } from '@core/utils/formatters'
  import { useRoute } from 'vue-router'
  import { useTheme } from 'vuetify'

  const router = useRouter()

  const props = defineProps({
    id: {
      type: String,
      required: false,
      default: null,
    },
  })

  interface Emit {
    (e: 'toggleForm', value: boolean): void
  }

  const userTab = ref(null)

  const tabs = [
    { icon: 'tabler-user-check', title: 'Stelle' },
    { icon: 'tabler-lock', title: 'Unternehmen' },
  ]

  const emit = defineEmits<Emit>()

  const authStore = useAuthStore()
  const jobAdFormStore = useJobAdFormStore()
  const companyStore = useCompanyStore()
  const subStore = useSubscriptionStore()

  const route = useRoute()
  const cid = route.params?.cid
  const jid = route.params?.id

  const {
    state: { jobAdText },
    actions: { handleSaveJobAd, handleEditJobAd },
  } = useJobAdverts()

  const {
    title,
    jobAdType,
    city,
    gehalt,
    gehalt2,
    gehalt3,
    startDate,
    headerSavedUrl,
    detailDescription,
    workHours,
    educationDuration,
    holidayDays,
    headerResult,
  } = jobAdFormStore

  const { company } = companyStore

  const loading = ref(false)
  const loadingPremium = ref(false)
  const loadingEdit = ref(false)

  const isDarkLayout = useTheme().global.current
  const isSuper = authStore.isSuperUser
  const submitDetails = async (type: string = 'draft') => {
    loading.value = type === 'draft'
    loadingPremium.value = type === 'premium'
    try {
      const jobSavedDet = await handleSaveJobAd(type)
      jobAdFormStore.$reset()
      await jobSaveSuccess(isDarkLayout.value.dark)

      const jobAdWithTitle = `${jobSavedDet.id}|${jobSavedDet.title}`
      await subStore.updateJobAdverts([jobAdWithTitle])
      localStorage.setItem('jobAdWithTitle', jobAdWithTitle)
      if (subStore?.getJobAdverts && subStore.getJobAdverts.length > 0) {
        await router.push({
          path: type === 'premium' ? '/company/subscription/checkout' : '/',
          query:
            type === 'premium'
              ? { ad: jobSavedDet.id, tit: jobSavedDet.title }
              : undefined,
        })
      }
    } catch (e) {
      console.log('error', e)
    } finally {
      loading.value = false
      loadingPremium.value = false
    }
  }
  const publishDraftJobAd = async () => {
    loadingPremium.value = true
    try {
      const jobSavedDet = jobAdFormStore
      loadingPremium.value = false
      await handleEditJobAd()

      const jobAdWithTitle = `${jobSavedDet.id}|${jobSavedDet.title}`
      await subStore.updateJobAdverts([jobAdWithTitle])
      localStorage.setItem('jobAdWithTitle', jobAdWithTitle)
      if (subStore?.getJobAdverts && subStore.getJobAdverts.length > 0) {
        await router.push({
          path: '/company/subscription/checkout',
          query: { ad: jobSavedDet.id, tit: jobSavedDet.title },
        })
      }
    } catch (e) {
      loadingPremium.value = false
      console.log('error', e)
    } finally {
      loadingPremium.value = false
    }
  }

  const editJobAd = async () => {
    const redRoute = isSuper ? `/super/company/${cid}` : '/company'
    const confirmSave = await confirmEditJobAd(isDarkLayout.value.dark)
    if (confirmSave.isConfirmed) {
      loadingEdit.value = true
      try {
        await handleEditJobAd()
        loadingEdit.value = false
        jobAdFormStore.$reset()
        await jobEditSuccess(isDarkLayout.value.dark)

        await router.push({
          path: redRoute,
        })
      } catch (e) {
        loadingEdit.value = false
        console.log('error', e)
      }
    }
  }

  onMounted(async () => {
    if (!title) showForm()
    await subStore.updateJobAdverts([])
  })

  const showForm = () => {
    emit('toggleForm', true)
  }
</script>

<template>
  <v-card class="pa-8">
    <VTabs v-model="userTab" class="v-tabs-pill" fixed-tabs>
      <VTab v-for="tab in tabs" :key="tab.icon">
        <span>{{ tab.title }}</span>
      </VTab>
    </VTabs>

    <VWindow
      v-model="userTab"
      class="mt-6 disable-tab-transition"
      :touch="false"
    >
      <VWindowItem>
        <VCard :flat="true" :subtitle="jobAdText.previewJobAdInfo">
          <template v-slot:title>
            <h1 class="text-h5 font-weight-bold text-center">
              {{ jobAdText.previewJobAd }}
            </h1>
          </template>
          <template v-slot:subtitle>
            <div class="text-subtitle-1 text-center">
              {{ jobAdText.previewJobAd }}
            </div>
          </template>
          <img class="align-end iphone-frame" :src="iphone" />
          <VCardText>
            <v-card
              class="mx-auto my-1 iphone-content"
              max-width="350"
              :flat="true"
            >
              <v-img
                class="align-end"
                cover
                height="280"
                :src="headerResult?.imageUrl || headerSavedUrl"
              >
                <!--                <v-avatar class="ma-3 mb-15" size="85" rounded="0">-->
                <!--                  <v-img :src="company?.headerImageUrl"></v-img>-->
                <!--                </v-avatar>-->
                <!--                <div>-->
                <!--                  <div class="text-h5 font-weight-bold text-white trans-bg">-->
                <!--                    {{ title }}-->
                <!--                    <p class="text-caption text-caption">-->
                <!--                      <v-icon icon="tabler-map-pin"></v-icon>-->
                <!--                      {{ city }}-->
                <!--                    </p>-->
                <!--                  </div>-->
                <!--                </div>-->
              </v-img>

              <v-row class="mx-1 mt-4" v-if="jobAdType === 'ausbildung'">
                <v-col>
                  <v-card-item>
                    <div class="text-h6 font-weight-bold">
                      {{ educationDuration }} {{ jobAdText.years }}
                    </div>
                    <v-card-subtitle>
                      <span class="me-1 text-caption text-align-end">{{
                        jobAdText.trainingDuration
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
                <v-col>
                  <v-card-item style="text-align: end">
                    <v-card-title>{{
                      startDate && formatDateToString(startDate, 'de')
                    }}</v-card-title>
                    <v-card-subtitle>
                      <span class="me-1 text-caption">{{
                        jobAdText.startOfTraining
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
              </v-row>
              <v-row class="mx-1 mt-4" v-if="jobAdType === 'ausbildung'">
                <v-col>
                  <v-card-item>
                    <v-card-title
                      >{{ holidayDays }} {{ jobAdText.days }}</v-card-title
                    >
                    <v-card-subtitle>
                      <span class="me-1 text-caption">{{
                        jobAdText.holidaysYear
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
                <v-col>
                  <v-card-item style="text-align: end">
                    <v-card-title
                      >{{ workHours }} {{ jobAdText.hours }}</v-card-title
                    >
                    <v-card-subtitle>
                      <span class="me-1 text-caption">{{
                        jobAdText.hoursWeek
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
              </v-row>
              <v-row class="mx-1 mt-4" v-if="jobAdType === 'ausbildung'">
                <v-col>
                  <v-card-item>
                    <v-card-title>{{ city }}</v-card-title>
                    <v-card-subtitle>
                      <span class="me-1 text-caption">{{
                        jobAdText.trainingLocation
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
                <v-col>
                  <v-card-item style="text-align: end">
                    <v-card-title>{{ gehalt }} €</v-card-title>
                    <v-card-subtitle>
                      <span class="me-1 text-caption">{{
                        jobAdText.salary1Year
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
              </v-row>
              <v-row
                class="mx-1 mt-4"
                v-if="jobAdType === 'ausbildung' && gehalt2"
              >
                <v-col>
                  <v-card-item>
                    <v-card-title>{{ gehalt2 }}</v-card-title>
                    <v-card-subtitle>
                      <span class="me-1 text-caption">{{
                        jobAdText.salary2Year
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
                <v-col>
                  <v-card-item style="text-align: end">
                    <v-card-title>{{ gehalt3 }} €</v-card-title>
                    <v-card-subtitle>
                      <span class="me-1 text-caption">{{
                        jobAdText.salary3Year
                      }}</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
              </v-row>
              <v-divider class="ma-4"></v-divider>
              <div class="pa-1 ma-4">
                <div
                  class="scroll-description"
                  v-html="detailDescription"
                ></div>
              </div>
            </v-card>
          </VCardText>
          <v-card-actions>
            <VBtn color="primary" variant="text" @click="showForm">
              <v-icon class="mr-1" icon="tabler-arrow-big-left-filled"></v-icon>
              Zurück
            </VBtn>
            <v-spacer> </v-spacer>

            <div v-if="props.id">
              <VBtn
                class="mx-6"
                type="submit"
                color="success"
                :loading="loadingEdit"
                :disabled="loadingEdit"
                variant="flat"
                @click="editJobAd"
              >
                {{ jobAdText.saveChanges }}
              </VBtn>
              <VBtn
                v-if="jobAdFormStore.isDraft"
                type="submit"
                color="gold"
                :loading="loadingPremium"
                :disabled="loadingPremium"
                append-icon="mdi-crown"
                variant="flat"
                @click="publishDraftJobAd"
              >
                {{ jobAdText.savePremium }}
              </VBtn>
            </div>
            <div v-else>
              <VBtn
                class="mx-6"
                type="submit"
                color="success"
                :loading="loading"
                :disabled="loading"
                variant="flat"
                @click="submitDetails('draft')"
              >
                {{ jobAdText.saveDraft }}
              </VBtn>

              <VBtn
                type="submit"
                color="gold"
                :loading="loadingPremium"
                :disabled="loading"
                append-icon="mdi-crown"
                variant="flat"
                @click="submitDetails('premium')"
              >
                {{ jobAdText.savePremium }}
              </VBtn>
            </div>
          </v-card-actions>
        </VCard>
      </VWindowItem>

      <!--     &#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; Window Item for company &#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->

      <VWindowItem>
        <VCard :flat="true">
          <template v-slot:title>
            <h1 class="text-h5 font-weight-bold text-center">
              Unternehmensdetails
            </h1>
          </template>
          <template v-slot:subtitle>
            <div class="text-subtitle-1 text-center">
              Dies sind die Angaben zu Ihrem Unternehmen
            </div>
          </template>
          <img class="align-end iphone-frame" :src="iphone" />
          <VCardText>
            <v-card
              class="mx-auto my-1 iphone-content"
              max-width="345"
              :flat="true"
            >
              <v-img
                class="align-end"
                cover
                height="280"
                :src="company?.headerImageUrl"
              >
                <v-avatar class="ma-3 logo-avatar" size="85" rounded="0">
                  <v-img
                    class="logo-image"
                    :src="company?.logoImageUrl"
                  ></v-img>
                </v-avatar>
                <div class="mt-16"></div>
                <!--                <div>-->
                <!--                  <div class="text-h5 font-weight-bold text-white trans-bg">-->
                <!--                    {{ company?.name }}-->
                <!--                    <p class="text-caption text-caption">-->
                <!--                      <v-icon icon="tabler-map-pin"></v-icon>-->
                <!--                      {{ company?.city }}-->
                <!--                    </p>-->
                <!--                  </div>-->
                <!--                </div>-->
              </v-img>
              <v-row class="mx-1 mt-4">
                <v-col>
                  <v-card-item>
                    <v-card-title>{{ company?.foundingYear }}</v-card-title>
                    <v-card-subtitle>
                      <span class="me-1 text-caption">Gründungsjahr</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
                <v-col>
                  <v-card-item style="text-align: end">
                    <v-card-title>{{ company?.totalEmployees }}</v-card-title>
                    <v-card-subtitle>
                      <span class="me-1 text-caption">Mitarbeiter</span>
                    </v-card-subtitle>
                  </v-card-item>
                </v-col>
              </v-row>
              <v-col class="mt-4">
                <v-card-item>
                  <div
                    class="font-weight-bold"
                    style="text-overflow: inherit; font-size: larger"
                  >
                    {{ company?.address }}
                  </div>
                  <v-card-subtitle>
                    <span class="me-1 text-caption">{{ company?.city }}</span>
                  </v-card-subtitle>
                </v-card-item>
              </v-col>
              <v-divider class="ma-4"></v-divider>
              <v-card-text>
                <div
                  class="scroll-description-company"
                  v-html="company?.detailContent"
                ></div>
              </v-card-text>
            </v-card>
          </VCardText>
          <v-card-actions>
            <VBtn color="primary" variant="text" @click="showForm">
              <v-icon class="mr-1" icon="tabler-arrow-big-left-filled"></v-icon>
              Zurück
            </VBtn>
            <v-spacer> </v-spacer>

            <div v-if="props.id">
              <VBtn
                class="mx-6"
                type="submit"
                color="success"
                :loading="loadingEdit"
                :disabled="loadingEdit"
                variant="flat"
                @click="editJobAd"
              >
                {{ jobAdText.saveChanges }}
              </VBtn>
              <VBtn
                v-if="jobAdFormStore.isDraft"
                type="submit"
                color="gold"
                :loading="loadingPremium"
                :disabled="loadingPremium"
                append-icon="mdi-crown"
                variant="flat"
                @click="publishDraftJobAd"
              >
                {{ jobAdText.savePremium }}
              </VBtn>
            </div>
            <div v-else>
              <VBtn
                class="mx-6"
                type="submit"
                color="success"
                :loading="loading"
                :disabled="loading"
                variant="flat"
                @click="submitDetails('draft')"
              >
                {{ jobAdText.saveDraft }}
              </VBtn>

              <VBtn
                type="submit"
                color="gold"
                :loading="loadingPremium"
                :disabled="loading"
                append-icon="mdi-crown"
                variant="flat"
                @click="submitDetails('premium')"
              >
                {{ jobAdText.savePremium }}
              </VBtn>
            </div>
          </v-card-actions>
        </VCard>
      </VWindowItem>
    </VWindow>
  </v-card>
</template>

<style lang="scss" scoped>
  .v-card-item {
    padding: 0 5px !important;
  }
  .v-col {
    padding: 0 5px 0 5px !important;
  }
  .trans-bg {
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px;
  }
  .iphone-content {
    position: absolute;
    left: 33%;
    top: 14%;
    border-radius: 45px;
  }
  .iphone-frame {
    position: sticky;
    left: 30%;
    top: 8%;
    border-radius: 20px;
    z-index: 0;
  }
  .scroll-description {
    height: 190px;
    width: 325px;
    overflow-y: auto;
    padding-right: 0.5rem;
  }
  .scroll-description-company {
    height: 220px;
    width: 325px;
    overflow-y: auto;
    padding-right: 0.5rem;
  }
  .logo-avatar {
    position: absolute;
    left: 1%;
    top: 4%;
    border-radius: 10px;
  }
</style>

<style>
  .scroll-description h1 {
    font-size: 18px !important;
    margin-bottom: 10px;
  }
</style>

<route lang="yaml">
meta:
  action: edit
  subject: JobAds
</route>
