<script setup lang="ts">
  import { ref, computed } from 'vue'
  import dayjs from 'dayjs'
  import utc from 'dayjs/plugin/utc'
  import { useAppStore } from '@/stores/appStore'
  import { useContactPersonTimeslots } from '@/composables/ContactPersonTimeslot/useContactPersonTimeslot'
  import { useContactPersonStore } from '@/stores/contact-person/contactPersonStore'
  import TimeslotAvailability from '@/components/Fairs/Company/TimeslotAvailability.vue'

  dayjs.extend(utc)

  interface Timeslot {
    id: string
    startTime: string
    endTime: string
    available: boolean
  }

  const {
    state: {
      combinedFairDaysAndTimeslots,
      formattedFairTimeslots,
      loadingCreate,
      loadingUpdate,
    },
    actions: {
      handleCreateCPTimeslot,
      handleRemoveCPTimeslot,
      handleUpdateCPTimeslot,
    },
  } = useContactPersonTimeslots()

  const appStore = useAppStore()
  const contactPersonStore = useContactPersonStore()
  const activeContactPersonName = computed(() => contactPersonStore?.name || '')
  const editingSlot = ref<string | null>(null)
  const startTimeTemp = ref('')
  const endTimeTemp = ref('')
  const availabilityTemp = ref('')
  const startTimeAdded = ref('00:00')
  const endTimeAdded = ref('00:15')
  const showAddTimeslotInput = ref<string | null>(null)
  const openPanel = ref<string[]>([])
  const selectedTimeslots = ref<string[]>([])

  const allAvailableTimeslotIds = computed(() => {
    const availableIds: string[] = []
    Object.values(combinedFairDaysAndTimeslots.value || {}).forEach(dayData => {
      dayData.slots.forEach(slot => {
        if (slot.available) {
          availableIds.push(slot.id)
        }
      })
    })
    return availableIds
  })

  const allAvailableSelected = computed({
    get: () => {
      if (allAvailableTimeslotIds.value.length === 0) return false
      return allAvailableTimeslotIds.value.every(id =>
        selectedTimeslots.value.includes(id),
      )
    },
    set: (value: boolean) => {
      if (value) {
        selectedTimeslots.value = [...allAvailableTimeslotIds.value]
      } else {
        selectedTimeslots.value = []
      }
    },
  })

  const getAvailableTimeslotsForDate = (dateSlots: Timeslot[]) => {
    return dateSlots.filter(slot => slot.available)
  }

  const areAllAvailableSelectedForDate = (dateSlots: Timeslot[]) => {
    const availableSlots = getAvailableTimeslotsForDate(dateSlots)
    if (availableSlots.length === 0) return false
    return availableSlots.every(slot =>
      selectedTimeslots.value.includes(slot.id),
    )
  }

  const toggleAllForDate = (dateSlots: Timeslot[]) => {
    const availableSlots = getAvailableTimeslotsForDate(dateSlots)
    const availableIds = availableSlots.map(slot => slot.id)

    if (areAllAvailableSelectedForDate(dateSlots)) {
      selectedTimeslots.value = selectedTimeslots.value.filter(
        id => !availableIds.includes(id),
      )
    } else {
      const newSelections = [
        ...new Set([...selectedTimeslots.value, ...availableIds]),
      ]
      selectedTimeslots.value = newSelections
    }
  }

  const startEditing = (slot: Timeslot) => {
    editingSlot.value = slot.id
    startTimeTemp.value = dayjs.utc(slot.startTime).format('HH:mm')
    endTimeTemp.value = dayjs.utc(slot.endTime).format('HH:mm')
    availabilityTemp.value = slot.available ? 'Available' : 'Occupied'
  }

  const saveEdit = async (slot: Timeslot) => {
    const date = dayjs.utc(slot.startTime).format('YYYY-MM-DD')
    if (
      dayjs
        .utc(`${date}T${startTimeTemp.value}`)
        .isAfter(`${date}T${endTimeTemp.value}`)
    ) {
      appStore.showSnack('Start time must be before end time')
      return
    }

    const updatedSlot = {
      id: slot?.id,
      startTime: `${date}T${startTimeTemp.value}:00.000Z`,
      endTime: `${date}T${endTimeTemp.value}:00.000Z`,
      available: availabilityTemp.value === 'Available',
    }
    await handleUpdateCPTimeslot(updatedSlot)
    appStore.showSnack('Timeslot Updated Successfully')
    editingSlot.value = null
  }

  const cancelEdit = () => {
    editingSlot.value = null
  }

  const showAddTimeslot = (dateKey: string) => {
    showAddTimeslotInput.value = dateKey
  }
  const hideAddTimeslot = () => {
    showAddTimeslotInput.value = null
  }

  const addNewTimeslot = async (dateKey: string) => {
    const input = {
      startTime: dayjs
        .utc(`${dateKey}T${startTimeAdded.value}:00.000Z`)
        .format(),
      endTime: dayjs.utc(`${dateKey}T${endTimeAdded.value}:00.000Z`).format(),
    }
    await handleCreateCPTimeslot(input)
    appStore.showSnack('Timeslot added successfully')
    hideAddTimeslot()
  }

  const removeTimeslot = async (slot: Timeslot) => {
    if (!slot.available) return
    await handleRemoveCPTimeslot(slot.id)
    appStore.showSnack('Timeslot removed successfully')
  }
  const bulkDeleteTimeslots = async () => {
    try {
      for (const slotId of selectedTimeslots.value) {
        await handleRemoveCPTimeslot(slotId)
      }
      appStore.showSnack('Selected timeslots deleted successfully')
      selectedTimeslots.value = []
    } catch (error) {
      appStore.showSnack('Failed to delete timeslots')
    }
  }

  watch(openPanel, newValue => {
    if (newValue.length === 0) {
      selectedTimeslots.value = []
    }
  })
</script>
<template>
  <div>
    <v-card class="mb-4 elevation-3" v-if="activeContactPersonName">
      <v-card-item class="bg-tertiary">
        <v-row>
          <v-col cols="8">
            <v-card-title class="text-white">
              <v-icon start>mdi-account</v-icon>
              {{ activeContactPersonName }}
            </v-card-title>
          </v-col>
          <v-col cols="4" class="d-flex justify-end align-center">
            <!-- Global Select All checkbox -->
            <v-checkbox
              v-if="allAvailableTimeslotIds.length > 0"
              v-model="allAvailableSelected"
              label="All"
              hide-details
              class="mr-4 text-white"
              color="white"
            ></v-checkbox>

            <v-btn
              v-if="selectedTimeslots.length > 0"
              color="error"
              icon="tabler-trash"
              variant="text"
              @click="bulkDeleteTimeslots"
            >
              <template v-slot:prepend>
                <v-badge
                  v-if="selectedTimeslots.length > 0"
                  color="error"
                  :content="selectedTimeslots.length"
                  inline
                ></v-badge>
              </template>
              <v-icon>tabler-trash</v-icon>
            </v-btn>
            <v-chip class="font-weight-bold ml-2">
              {{ formattedFairTimeslots.length }} Total
            </v-chip>
          </v-col>
        </v-row>
      </v-card-item>
    </v-card>

    <v-expansion-panels v-model="openPanel" multiple>
      <v-expansion-panel
        v-for="(data, date) in combinedFairDaysAndTimeslots"
        :key="date"
        :value="date"
      >
        <v-expansion-panel-title>
          <template v-slot:default="{ expanded }">
            <v-row align="center">
              <v-col cols="6">
                <v-icon start>mdi-calendar</v-icon>
                <span class="text-h6">{{ data?.formattedDate }}</span>
              </v-col>
              <v-col cols="6" class="d-flex justify-end">
                <v-chip variant="tonal" color="primary">
                  {{ data.slots.length }} Slots
                </v-chip>
              </v-col>
            </v-row>
          </template>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <!-- Date-specific Select All checkbox -->
          <div
            v-if="getAvailableTimeslotsForDate(data.slots).length > 0"
            class="mb-3"
          >
            <v-checkbox
              :model-value="areAllAvailableSelectedForDate(data.slots)"
              @update:model-value="toggleAllForDate(data.slots)"
              :label="`Select All Available (${
                getAvailableTimeslotsForDate(data.slots).length
              })`"
              hide-details
              color="primary"
            ></v-checkbox>
          </div>

          <v-list>
            <v-list-item
              v-for="slot in data.slots"
              :key="slot.id"
              :class="{ 'bg-grey-lighten-3': editingSlot === slot.id }"
            >
              <template v-if="editingSlot === slot.id">
                <v-row align="center" class="pa-2">
                  <v-col cols="4">
                    <v-text-field
                      :loading="loadingUpdate"
                      :disabled="loadingUpdate"
                      v-model="startTimeTemp"
                      label="Start Time"
                      type="time"
                      density="compact"
                      variant="outlined"
                      hide-details
                    />
                  </v-col>
                  <v-col cols="4">
                    <v-text-field
                      :loading="loadingUpdate"
                      :disabled="loadingUpdate"
                      @keydown.enter="saveEdit(slot)"
                      v-model="endTimeTemp"
                      label="End Time"
                      type="time"
                      density="compact"
                      variant="outlined"
                      hide-details
                    />
                  </v-col>
                  <v-col cols="2">
                    <TimeslotAvailability v-model="availabilityTemp" />
                  </v-col>
                  <v-col cols="2" class="d-flex justify-end">
                    <v-btn
                      :loading="loadingUpdate"
                      :disabled="loadingUpdate"
                      icon="mdi-check"
                      size="small"
                      color="success"
                      variant="text"
                      class="mr-2"
                      @click="saveEdit(slot)"
                    />
                    <v-btn
                      :disabled="loadingUpdate"
                      icon="mdi-close"
                      size="small"
                      color="error"
                      variant="text"
                      @click="cancelEdit"
                    />
                  </v-col>
                </v-row>
              </template>

              <template v-else>
                <v-list-item-title class="d-flex align-center">
                  <!-- Checkbox for selection - disabled for occupied timeslots -->
                  <v-checkbox
                    v-model="selectedTimeslots"
                    :value="slot.id"
                    :disabled="!slot.available"
                    hide-details
                    class="mr-2"
                  ></v-checkbox>

                  <v-chip class="ma-2 px-2 mr-8" color="success" label>
                    <v-icon icon="mdi-clock-outline" start></v-icon>
                    {{ slot.formattedStart }} - {{ slot.formattedEnd }}
                  </v-chip>
                  <v-icon
                    size="small"
                    class="mr-2"
                    :color="slot.available ? 'success' : 'primary'"
                  >
                    {{ slot.available ? 'tabler-door' : 'tabler-door-off' }}
                  </v-icon>
                  <div
                    :class="
                      slot.available
                        ? 'text-caption text-success'
                        : 'text-caption text-primary'
                    "
                  >
                    {{ slot.available ? 'Available' : 'Occupied' }}
                  </div>
                  <v-spacer />

                  <v-btn
                    v-bind="props"
                    icon="mdi-pencil"
                    size="small"
                    color="primary"
                    variant="text"
                    @click="startEditing(slot)"
                  >
                    <v-icon>mdi-pencil</v-icon>
                    <v-tooltip activator="parent" location="start"
                      >Edit</v-tooltip
                    >
                  </v-btn>

                  <v-btn
                    v-if="slot.available"
                    v-bind="props"
                    icon="mdi-minus"
                    size="small"
                    color="error"
                    variant="text"
                    @click="removeTimeslot(slot)"
                  >
                    <v-icon>mdi-minus</v-icon>
                    <v-tooltip activator="parent" location="start">{{
                      slot.available
                        ? 'Remove'
                        : 'Cannot remove occupied timeslot.'
                    }}</v-tooltip>
                  </v-btn>
                </v-list-item-title>
              </template>
            </v-list-item>

            <template v-if="showAddTimeslotInput === date">
              <v-row align="center" class="pa-2">
                <v-col cols="4">
                  <v-text-field
                    :loading="loadingUpdate || loadingCreate"
                    :disabled="loadingUpdate || loadingCreate"
                    v-model="startTimeAdded"
                    label="Start Time"
                    type="time"
                    density="compact"
                    variant="outlined"
                    hide-details
                  />
                </v-col>
                <v-col cols="4">
                  <v-text-field
                    :loading="loadingUpdate || loadingCreate"
                    :disabled="loadingUpdate || loadingCreate"
                    @keydown.enter="addNewTimeslot(date)"
                    v-model="endTimeAdded"
                    label="End Time"
                    type="time"
                    density="compact"
                    variant="outlined"
                    hide-details
                  />
                </v-col>
                <v-col cols="4" class="d-flex justify-end">
                  <v-btn
                    :loading="loadingCreate || loadingUpdate"
                    :disabled="loadingCreate"
                    icon="mdi-check"
                    size="small"
                    color="success"
                    variant="text"
                    class="mr-2"
                    @click="addNewTimeslot(date)"
                  />
                  <v-btn
                    :disabled="loadingUpdate || loadingCreate"
                    icon="mdi-close"
                    size="small"
                    color="error"
                    variant="text"
                    @click="hideAddTimeslot"
                  />
                </v-col>
              </v-row>
            </template>
          </v-list>

          <v-card-actions class="pa-4">
            <v-btn
              block
              color="primary"
              variant="flat"
              prepend-icon="mdi-plus"
              @click="showAddTimeslot(date)"
              :disabled="showAddTimeslotInput === date"
            >
              Add Timeslot
            </v-btn>
          </v-card-actions>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </div>
</template>

<style scoped lang="scss">
  .v-list-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  }

  .v-chip {
    font-size: 0.875rem;
    font-weight: 500;
  }

  .v-btn {
    transition: opacity 0.2s ease-in-out;
  }

  .v-btn:hover {
    opacity: 0.8;
  }
</style>
