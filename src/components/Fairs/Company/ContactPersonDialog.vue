<script setup lang="ts">
  import { useContactPersonStore } from '@/stores/contact-person/contactPersonStore'
  import { useContactPersons } from '@/composables/ContactPersons/useContactPersons'
  import { useAppStore } from '@/stores/appStore'
  import { emailValidator, requiredValidator } from '@validators'

  const {
    state: { loadingCreateContactPerson },
    actions: { handleCreateContactPerson, handleUpdateContactPerson },
  } = useContactPersons()

  const contactPersonStore = useContactPersonStore()

  const appStore = useAppStore()

  const resetForm = () => {
    contactPersonStore.toggleContactPersonForm()
    contactPersonStore.resetForm()
  }

  const onFormSubmit = async () => {
    try {
      if (!contactPersonStore.name || !contactPersonStore.email) {
        appStore.showSnack('Please fill all fields')
        return
      }
      if (contactPersonStore.id) {
        await handleUpdateContactPerson()
        appStore.showSnack('Contact person updated successfully')
        return
      } else {
        await handleCreateContactPerson()
        appStore.showSnack('Contact person added successfully')
      }
      resetForm()
    } catch (error) {
      console.error('Error creating contact person:', error)
    }
  }

  const dialogModelValueUpdate = () => {
    contactPersonStore.toggleContactPersonForm()
    contactPersonStore.resetForm()
  }
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 650"
    :model-value="contactPersonStore.showContactPersonForm"
    @update:model-value="dialogModelValueUpdate"
  >
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard class="pa-sm-9 pa-5">
      <VCardItem v-if="contactPersonStore.id">
        <VCardTitle class="text-h5 text-center mb-3">
          Update Contact Person
        </VCardTitle>
        <p class="text-center">Update Contact person details</p>
      </VCardItem>
      <VCardItem v-else>
        <VCardTitle class="text-h5 text-center mb-3">
          Add New Contact Person
        </VCardTitle>
        <p class="text-center">Add a new contact person to the company</p>
      </VCardItem>

      <VCardText>
        <VForm @submit.prevent="onFormSubmit">
          <VRow>
            <VCol cols="12">
              <VTextField
                v-model="contactPersonStore.name"
                label="Full Name"
                prepend-inner-icon="tabler-user"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol cols="12">
              <VTextField
                v-model="contactPersonStore.email"
                label="Email"
                type="email"
                prepend-inner-icon="tabler-mail"
                :rules="[requiredValidator, emailValidator]"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="contactPersonStore.position"
                label="Position"
                prepend-inner-icon="tabler-briefcase"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="contactPersonStore.phone"
                label="Phone Number"
                prepend-inner-icon="tabler-phone"
              />
            </VCol>

            <VCol cols="12" class="text-center">
              <VBtn
                :loading="loadingCreateContactPerson"
                :disabled="loadingCreateContactPerson"
                type="submit"
                class="me-3"
              >
                Submit
              </VBtn>

              <VBtn variant="tonal" color="secondary" @click="resetForm">
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
