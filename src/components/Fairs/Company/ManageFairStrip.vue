<script setup lang="ts">
  import { useAuthStore } from '@/stores/authStore'
  import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
  import { useCompanyStore } from '@/stores/companyStore'
  import { ref } from 'vue'

  const authStore = useAuthStore()
  const {
    actions: { manageFair },
    state: { loadingManageFair },
  } = useCompanyGraph()

  const companyStore = useCompanyStore()
  const confirmDialog = ref(false)
  const isSwitchingToManaged = ref<boolean | null>(null)

  const isFGAdminManaged = computed(
    () => companyStore.company?.isFairManaged || false,
  )

  const handleSwitch = (manage: boolean) => {
    isSwitchingToManaged.value = manage
    confirmDialog.value = true
  }

  const onConfirmSwitch = async () => {
    if (isSwitchingToManaged.value !== null && authStore.companyId) {
      await manageFair({
        companyId: authStore.companyId,
        isFairManaged: isSwitchingToManaged.value
      })
      companyStore.updateCompanyManagedStatus(isSwitchingToManaged.value)
      confirmDialog.value = false
      isSwitchingToManaged.value = null
    } else {
      console.error('Company ID not found or invalid manage value.')
    }
  }

  const onCloseDialog = () => {
    confirmDialog.value = false
    isSwitchingToManaged.value = null
  }
</script>

<template>
  <div>
    <v-alert
      v-if="!isFGAdminManaged"
      title="Benachrichtigungen erhalten"
      variant="tonal"
    >
      <template v-slot:text>
        Möchten Sie über neue Terminanfragen benachrichtigt werden?
      </template>
      <v-btn
        :loading="loadingManageFair"
        :disabled="loadingManageFair"
        size="small"
        color="success"
        @click="handleSwitch(true)"
        class="mr-2"
      >
        Ja
      </v-btn>
    </v-alert>

    <v-alert v-else title="Benachrichtigungen erhalten" variant="tonal">
      <template v-slot:text>
        Möchten Sie über neue Terminanfragen benachrichtigt werden?
      </template>
      <v-btn
        :loading="loadingManageFair"
        :disabled="loadingManageFair"
        size="small"
        color="warning"
        @click="handleSwitch(false)"
        class="mr-2"
      >
        Nein
      </v-btn>
    </v-alert>

    <v-dialog v-model="confirmDialog" persistent width="auto">
      <v-card>
        <v-card-title>
          <span class="text-h5">Bestätigung</span>
        </v-card-title>
        <v-card-text>
          Sind Sie sicher, dass Sie die Benachrichtigungen für neue
          Terminanfragen
          {{ isSwitchingToManaged ? 'aktivieren' : 'deaktivieren' }} möchten?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey-darken-1" variant="text" @click="onCloseDialog">
            Abbrechen
          </v-btn>
          <v-btn
            color="primary"
            :loading="loadingManageFair"
            :disabled="loadingManageFair"
            @click="onConfirmSwitch"
          >
            Bestätigen
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped lang="scss"></style>
