<script setup lang="ts">
  import { useCategory } from '@/composables/Category/useCategory'
  import { useFairs } from '@/composables/Fairs/useFairs'
  import { useCompanyFairParticipation } from '@/composables/CompanyFairParticipation/useCompanyFairParticipation'
  import { onMounted } from 'vue'
  import { useCompanyContactPersonGraphData } from '@/api/graphHooks/companyContactPerson/useCompanyContactPersonGraphData'

  const {
    state: { companyCategories, fairParticipationByFairAndCompany },
    actions: { loadContactPersonsAndJobsInFair },
  } = useCompanyContactPersonGraphData()

  const partId = computed(() => fairParticipationByFairAndCompany.value.id)

  let chips = ref([])

  const showEdit = ref(false)

  const {
    state: { categoryList },
    actions: { loadCategories },
  } = useCategory()

  const {
    state: { loadingUpdate },
    actions: { handleUpdateCategories },
  } = useCompanyFairParticipation()

  // Load categories when component mounts
  onMounted(async () => {
    await loadCategories()
    await loadContactPersonsAndJobsInFair()
  })

  const categoriesAction = () => {
    if (showEdit) {
      saveCategories()
    } else {
      toggleEditCategory()
    }
  }

  const saveCategories = () => {
    const catIds = chips.value.map((chip: { id: string }) => chip.id)
    handleUpdateCategories(partId.value, catIds)
    toggleEditCategory()
  }

  const toggleEditCategory = () => {
    chips.value = companyCategories.value
    showEdit.value = !showEdit.value
  }
</script>

<template>
  <v-skeleton-loader
    v-if="loadingUpdate"
    class="mx-auto"
    elevation="2"
    type=" sentences,  button"
  ></v-skeleton-loader>

  <v-card class="mx-auto mt-4" v-else>
    <v-row class="pa-3">
      <v-col cols="6">
        <v-toolbar-title>{{ $t('Company Categories') }}</v-toolbar-title>
      </v-col>
      <v-col cols="6" class="d-flex justify-end">
        <v-btn
          class="ml-4"
          @click="categoriesAction"
          prepend-icon="mdi-plus"
          size="small"
          variant="outlined"
          :color="showEdit ? 'success' : 'primary'"
        >
          {{ showEdit ? $t('Save New Categories') : $t('Add') }}
        </v-btn>
      </v-col>
    </v-row>

    <v-divider></v-divider>

    <v-card-text v-if="!showEdit">
      <v-chip-group v-model="companyCategories" column multiple>
        <v-chip
          v-for="category in companyCategories"
          :key="category.id"
          :text="category.name"
          variant="outlined"
        >
          <v-avatar start>
            <v-img :src="category.imageUrl"></v-img>
          </v-avatar>
          {{ category.name }}
        </v-chip>
      </v-chip-group>
    </v-card-text>

    <v-card-text v-else>
      <v-combobox
        v-model="chips"
        :items="categoryList"
        item-text="name"
        item-value="id"
        item-title="name"
        return-object
        label="All Categories"
        variant="solo"
        chips
        clearable
        closable-chips
        multiple
      >
        <template v-slot:chip="{ props, item }">
          <v-chip v-bind="props">
            <v-avatar start>
              <v-img :src="(item.raw as any).imageUrl"></v-img>
            </v-avatar>
            <strong>{{ item.raw.name }}</strong
            >&nbsp;
          </v-chip>
        </template>
      </v-combobox>
    </v-card-text>
  </v-card>
</template>

<style scoped lang="scss"></style>
