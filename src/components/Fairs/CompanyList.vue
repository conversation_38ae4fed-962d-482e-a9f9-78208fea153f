<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useCompanyStore } from '@/stores/companyStore'
  import { useAppStore } from '@/stores/appStore'
  import { useFairStore } from '@/stores/fairs/fairStore'
  import { useCompanyFairParticipation } from '@/composables/CompanyFairParticipation/useCompanyFairParticipation'
  import { useCompanyGraph } from '@/api/graphHooks/useCompanyGraph'
  import { useCompany } from '@/composables/Company/useCompany'
  import { useFairCompaniesGraph } from '@/api/graphHooks/company/useFairCompaniesGraph'

  const appStore = useAppStore()
  const fairStore = useFairStore()
  const companyStore = useCompanyStore()
  const showSearch = ref(true)
  const searchQuery = ref('')
  const loadingCompanyId = ref('')

  const {
    state: { loadingCompaniesForFairList, companiesForFairList },
  } = useFairCompaniesGraph()

  const {
    actions: { handleCreateParticipation },
  } = useCompanyFairParticipation()

  const toggleSearch = () => {
    showSearch.value = !showSearch.value
    if (!showSearch.value) {
      searchQuery.value = ''
    }
  }

  const closeList = () => {
    fairStore.toggleCompanyList()
  }

  const filteredCompanies = computed(() => {
    return (companiesForFairList.value || []).filter((fair: { name: string }) =>
      fair.name.toLowerCase().includes(searchQuery.value.toLowerCase()),
    )
  })

  const totalCompaniesInFair = computed(() => {
    return companiesForFairList.value?.filter(
      (company: any) => company.isInFair,
    ).length
  })

  const totalCompanies = computed(() => {
    return companiesForFairList.value?.length
  })

  const addCompanyToFair = async (company: { id: string }) => {
    try {
      loadingCompanyId.value = company.id

      await handleCreateParticipation(company.id)
      appStore.showSnack('Company added to the fair')
      companyStore.updateCompanyInFair(company.id)
    } catch (error) {
      console.error('Error adding company to fair:', error)
      appStore.showSnack('Failed to add company to the fair')
    } finally {
      loadingCompanyId.value = ''
    }
  }
</script>

<template>
  <v-card class="mx-auto" max-width="500">
    <v-layout>
      <v-app-bar color="pink">
        <v-btn @click="closeList" icon="mdi-close" aria-label="Close"></v-btn>
        <v-toolbar-title>{{ $t('Companies') }}</v-toolbar-title>
        <v-chip class="font-weight-bold ml-2">
          {{ totalCompaniesInFair }} / {{ totalCompanies }} Total
        </v-chip>
        <v-spacer></v-spacer>
        <v-btn
          @click="toggleSearch"
          icon="mdi-magnify"
          aria-label="Search"
        ></v-btn>
      </v-app-bar>

      <v-main>
        <v-container>
          <v-row v-if="showSearch">
            <v-col cols="12">
              <v-text-field
                v-model="searchQuery"
                :label="$t('Find Company')"
                single-line
                hide-details
                @click:clear="searchQuery = ''"
                clearable
                aria-label="Search Contact Persons"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row dense class="scrollable-container">
            <v-col v-if="loadingCompaniesForFairList" cols="12">
              <v-skeleton-loader type="card"></v-skeleton-loader>
            </v-col>

            <!-- Empty State -->
            <v-col v-else-if="filteredCompanies.length === 0" cols="12">
              <v-alert color="primary" class="text-center">
                {{ $t('No Companies added') }}
              </v-alert>
            </v-col>

            <v-col
              v-for="(company, index) in filteredCompanies"
              :key="index"
              cols="12"
            >
              <v-card
                class="pa-2 list"
                :disabled="company.isInFair || loadingCompanyId === company.id"
                :loading="loadingCompanyId === company.id"
                hover
              >
                <div class="d-flex flex-no-wrap justify-space-between">
                  <div>
                    <v-card-title class="text-h6">
                      <v-row>
                        <v-col cols="9">
                          <v-avatar size="36px" v-if="company.logoImageUrl">
                            <v-img
                              alt="Avatar"
                              :src="company.logoImageUrl"
                            ></v-img>
                          </v-avatar>

                          <v-icon v-else class="mr-2" size="small"
                            >tabler-user-square
                          </v-icon>
                          {{
                            company.name.length > 30
                              ? company.name.slice(0, 30) + '...'
                              : company.name
                          }}
                        </v-col>
                      </v-row>
                    </v-card-title>
                    <v-row class="ml-1 my-1">
                      <v-chip
                        variant="outlined"
                        v-if="company.city"
                        class="ma-1"
                        prepend-icon="mdi-domain"
                        color="primary"
                      >
                        {{ company.city }}
                      </v-chip>
                      <v-chip
                        v-if="company.address"
                        class="ma-1"
                        prepend-icon="mdi-map-marker"
                        color="secondary"
                      >
                        {{ company.address }}
                      </v-chip>
                    </v-row>
                  </div>
                </div>

                <v-list-item
                  v-if="!company.isInFair"
                  prepend-icon="mdi-plus"
                  @click="addCompanyToFair(company)"
                  :subtitle="$t('Add this company to the fair')"
                  link
                ></v-list-item>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-main>
    </v-layout>
  </v-card>
</template>

<style scoped lang="scss">
  .scrollable-container {
    max-height: 650px; /* Adjust height as needed */
    overflow-y: auto;
    padding-right: 8px; /* Add padding to avoid scrollbar overlap */

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e32753;
      border-radius: 50px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #e32753;
    }
  }

  .v-card .list {
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .action-btn {
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
</style>
