<script setup lang="ts">
  import dayjs from 'dayjs'
  import utc from 'dayjs/plugin/utc'
  import { useTimeslots } from '@/composables/Timeslot/useTimeslot'
  import { useAppStore } from '@/stores/appStore'

  dayjs.extend(utc)

  interface Timeslot {
    id: string
    startTime: string
    endTime: string
  }

  const {
    state: {
      groupedFairTimeslots,
      formattedFairTimeslots,
      loadingCreate,
      loadingUpdate,
      loadingBulkRemove,
    },
    actions: {
      handleCreateTimeslot,
      handleRemoveTimeslot,
      handleUpdateTimeslot,
      handleRemoveMultipleTimeslots,
      loadTimeslots,
    },
  } = useTimeslots()

  onMounted(async () => {
    await loadTimeslots()
  })

  const appStore = useAppStore()

  const editingSlot = ref<string | null>(null)
  const startTimeTemp = ref('')
  const endTimeTemp = ref('')
  const startTimeAdded = ref('00:00')
  const endTimeAdded = ref('00:15')
  const showAddTimeslotInput = ref<string | null>(null) // Track which date column is in "add timeslot" mode
  const selectedTimeslots = ref<string[]>([])

  const allTimeslotIds = computed(() => {
    const allIds: string[] = []
    Object.values(groupedFairTimeslots.value || {}).forEach(slots => {
      slots.forEach(slot => {
        allIds.push(slot.id)
      })
    })
    return allIds
  })

  const allSelected = computed({
    get: () => {
      if (allTimeslotIds.value.length === 0) return false
      return allTimeslotIds.value.every(id =>
        selectedTimeslots.value.includes(id),
      )
    },
    set: (value: boolean) => {
      if (value) {
        selectedTimeslots.value = [...allTimeslotIds.value]
      } else {
        selectedTimeslots.value = []
      }
    },
  })

  const areAllSelectedForDate = (dateSlots: Timeslot[]) => {
    if (dateSlots.length === 0) return false
    return dateSlots.every(slot => selectedTimeslots.value.includes(slot.id))
  }

  const toggleAllForDate = (dateSlots: Timeslot[]) => {
    const slotIds = dateSlots.map(slot => slot.id)

    if (areAllSelectedForDate(dateSlots)) {
      selectedTimeslots.value = selectedTimeslots.value.filter(
        id => !slotIds.includes(id),
      )
    } else {
      const newSelections = [
        ...new Set([...selectedTimeslots.value, ...slotIds]),
      ]
      selectedTimeslots.value = newSelections
    }
  }

  const startEditing = (slot: Timeslot) => {
    editingSlot.value = slot.id
    startTimeTemp.value = dayjs.utc(slot.startTime).format('HH:mm')
    endTimeTemp.value = dayjs.utc(slot.endTime).format('HH:mm')
  }

  const saveEdit = async (slot: Timeslot) => {
    const date = dayjs.utc(slot.startTime).format('YYYY-MM-DD')
    if (
      dayjs
        .utc(`${date}T${startTimeTemp.value}`)
        .isAfter(`${date}T${endTimeTemp.value}`)
    ) {
      appStore.showSnack('Start time must be before end time')
      return
    }
    const updatedSlot = {
      startTime: `${date}T${startTimeTemp.value}:00.000Z`,
      endTime: `${date}T${endTimeTemp.value}:00.000Z`,
    }
    console.log({ slot, date, updatedSlot })
    await handleUpdateTimeslot(slot.id, updatedSlot)
    editingSlot.value = null
  }

  const cancelEdit = () => {
    editingSlot.value = null
  }

  const showAddTimeslot = (dateKey: string) => {
    showAddTimeslotInput.value = dateKey
  }
  const hideAddTimeslot = () => {
    showAddTimeslotInput.value = null
  }

  const addNewTimeslot = async (dateKey: string) => {
    const input = {
      startTime: dayjs
        .utc(`${dateKey}T${startTimeAdded.value}:00.000Z`)
        .format(),
      endTime: dayjs.utc(`${dateKey}T${endTimeAdded.value}:00.000Z`).format(),
    }
    console.log(input)
    await handleCreateTimeslot(input)
    hideAddTimeslot()
  }

  const removeTimeslot = async (slot: Timeslot) => {
    await handleRemoveTimeslot(slot.id)
  }

  const bulkDeleteTimeslots = async () => {
    try {
      await handleRemoveMultipleTimeslots(selectedTimeslots.value)
      selectedTimeslots.value = []
    } catch (error) {
      console.error('Failed to delete timeslots:', error)
    }
  }
</script>

<template>
  <div>
    <!-- Header with global controls -->
    <v-row>
      <v-col cols="8" class="d-flex justify-start ml-8 align-center">
        <v-checkbox
          v-if="allTimeslotIds.length > 0"
          v-model="allSelected"
          label="Alle auswählen"
          hide-details
          class="mr-4 text-white"
          color="white"
        ></v-checkbox>

        <v-btn
          v-if="selectedTimeslots.length > 0"
          color="error"
          variant="text"
          class="ml-8"
          @click="bulkDeleteTimeslots"
          :loading="loadingBulkRemove"
        >
          <template v-slot:append>
            <v-badge
              v-if="selectedTimeslots.length > 0"
              color="error"
              :content="selectedTimeslots.length"
              inline
            ></v-badge>
          </template>
          <v-icon>tabler-trash</v-icon> Delete
        </v-btn>
      </v-col>
      <v-col cols="3" class="d-flex justify-end align-center">
        <v-chip class="font-weight-bold ml-2">
          {{ formattedFairTimeslots.length }} Total
        </v-chip>
      </v-col>
    </v-row>

    <v-container>
      <v-row>
        <v-col
          v-for="(slots, date) in groupedFairTimeslots"
          :key="date"
          cols="12"
          md="6"
        >
          <v-card class="elevation-3">
            <v-card-title class="d-flex align-center">
              <v-icon start color="white">mdi-calendar</v-icon>
              <span class="text-white">{{ slots[0]?.formattedDate }}</span>
              <v-spacer></v-spacer>
              <v-checkbox
                v-if="slots.length > 0"
                :model-value="areAllSelectedForDate(slots)"
                @update:model-value="toggleAllForDate(slots)"
                label="All"
                hide-details
                class="mr-4 text-white"
                color="white"
              ></v-checkbox>
              <v-chip>{{ slots.length }} Slots</v-chip>
            </v-card-title>

            <v-list>
              <v-list-item
                v-for="slot in slots"
                :key="slot.id"
                :class="{ 'bg-grey-lighten-3': editingSlot === slot.id }"
              >
                <template v-if="editingSlot === slot.id">
                  <v-row align="center" class="pa-2">
                    <v-col cols="4">
                      <v-text-field
                        :loading="loadingUpdate"
                        :disabled="loadingUpdate"
                        v-model="startTimeTemp"
                        label="Start Time"
                        type="time"
                        density="compact"
                        variant="outlined"
                        hide-details
                      />
                    </v-col>
                    <v-col cols="4">
                      <v-text-field
                        :loading="loadingUpdate"
                        :disabled="loadingUpdate"
                        @keydown.enter="saveEdit(slot)"
                        v-model="endTimeTemp"
                        label="End Time"
                        type="time"
                        density="compact"
                        variant="outlined"
                        hide-details
                      />
                    </v-col>
                    <v-col cols="4" class="d-flex justify-end">
                      <VBtn
                        :loading="loadingUpdate"
                        :disabled="loadingUpdate"
                        icon="mdi-check"
                        size="small"
                        color="success"
                        variant="text"
                        class="mr-2"
                        @click="saveEdit(slot)"
                      />
                      <VBtn
                        :disabled="loadingUpdate"
                        icon="mdi-close"
                        size="small"
                        color="error"
                        variant="text"
                        @click="cancelEdit"
                      />
                    </v-col>
                  </v-row>
                </template>

                <template v-else>
                  <v-list-item-title class="d-flex align-center">
                    <!-- Checkbox for selection -->
                    <v-checkbox
                      v-model="selectedTimeslots"
                      :value="slot.id"
                      hide-details
                      class="mr-2"
                    ></v-checkbox>

                    <v-chip class="ma-2 px-2 mr-8" color="success" label>
                      <v-icon icon="mdi-clock-outline" start></v-icon>
                      {{ slot.formattedStart }} - {{ slot.formattedEnd }}
                    </v-chip>
                    <v-spacer />
                    <v-btn
                      icon="mdi-pencil"
                      size="small"
                      color="primary"
                      variant="text"
                      @click="startEditing(slot)"
                    />
                    <v-btn
                      icon="mdi-minus"
                      size="small"
                      color="error"
                      variant="text"
                      @click="removeTimeslot(slot)"
                    />
                  </v-list-item-title>
                </template>
              </v-list-item>
              <template v-if="showAddTimeslotInput === date">
                <v-row align="center" class="pa-2">
                  <v-col cols="4">
                    <v-text-field
                      :loading="loadingUpdate"
                      :disabled="loadingUpdate"
                      v-model="startTimeAdded"
                      label="Start Time"
                      type="time"
                      density="compact"
                      variant="outlined"
                      hide-details
                    />
                  </v-col>
                  <v-col cols="4">
                    <v-text-field
                      :loading="loadingUpdate"
                      :disabled="loadingUpdate"
                      @keydown.enter="addNewTimeslot(String(date))"
                      v-model="endTimeAdded"
                      label="End Time"
                      type="time"
                      density="compact"
                      variant="outlined"
                      hide-details
                    />
                  </v-col>
                  <v-col cols="4" class="d-flex justify-end">
                    <VBtn
                      :loading="loadingUpdate"
                      :disabled="loadingUpdate"
                      icon="mdi-check"
                      size="small"
                      color="success"
                      variant="text"
                      class="mr-2"
                      @click="addNewTimeslot(String(date))"
                    />
                    <VBtn
                      :disabled="loadingUpdate"
                      icon="mdi-close"
                      size="small"
                      color="error"
                      variant="text"
                      @click="hideAddTimeslot"
                    />
                  </v-col>
                </v-row>
              </template>
            </v-list>

            <v-card-actions class="pa-4">
              <v-btn
                block
                color="primary"
                variant="flat"
                prepend-icon="mdi-plus"
                @click="showAddTimeslot(String(date))"
                :disabled="showAddTimeslotInput === String(date)"
              >
                Add Timeslot
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<style scoped lang="scss">
  .v-list-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  }

  .v-card-title.bg-primary {
    background-color: #1976d2; /* Primary color */
  }

  .v-chip {
    font-size: 0.875rem;
    font-weight: 500;
  }

  .v-btn {
    transition: opacity 0.2s ease-in-out;
  }

  .v-btn:hover {
    opacity: 0.8;
  }
</style>
