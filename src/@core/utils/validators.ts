import { isEmpty, isEmptyArray, isNullOrUndefined } from './index'

// 👉 Required Validator
export const requiredValidator = (value: unknown) => {
  if (isNullOrUndefined(value) || isEmptyArray(value) || value === false)
    return 'Dieses Feld muss ausgefüllt werden'

  return !!String(value).trim().length || 'Dieses Feld muss ausgefüllt werden'
}

// 👉 Email Validator
export const emailValidator = (value: unknown) => {
  if (isEmpty(value)) return true

  const re =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

  if (Array.isArray(value))
    return (
      value.every(val => re.test(String(val))) ||
      'The Email field must be a valid email'
    )

  return re.test(String(value)) || 'The Email field must be a valid email'
}

// 👉 Password Validator
export const passwordValidator = (password: string) => {
  const regExp = /(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%&*()]).{8,}/

  const validPassword = regExp.test(password)

  return (
    // eslint-disable-next-line operator-linebreak
    validPassword ||
    'Passwort: Mindestens 8 Zeichen, mit Großbuchstaben, Kleinbuchstaben, Ziffer und Sonderzeichen'
  )
}

// 👉 Confirm Password Validator
export const confirmedValidator = (value: string, target: string) =>
  value === target || 'Passwörter stimmen nicht überein.'

// 👉 Between Validator
export const betweenValidator = (value: unknown, min: number, max: number) => {
  const valueAsNumber = Number(value)

  return (
    (Number(min) <= valueAsNumber && Number(max) >= valueAsNumber) ||
    `Enter number between ${min} and ${max}`
  )
}

// 👉 Integer Validator
export const integerValidator = (value: unknown) => {
  if (isEmpty(value)) return true

  if (Array.isArray(value))
    return (
      value.every(val => /^-?[0-9]+$/.test(String(val))) ||
      'This field must be an integer'
    )

  return /^-?[0-9]+$/.test(String(value)) || 'This field must be an integer'
}

// 👉 Regex Validator
export const regexValidator = (
  value: unknown,
  regex: RegExp | string,
): string | boolean => {
  if (isEmpty(value)) return true

  let regeX = regex
  if (typeof regeX === 'string') regeX = new RegExp(regeX)

  if (Array.isArray(value))
    return value.every(val => regexValidator(val, regeX))

  return regeX.test(String(value)) || 'The Regex field format is invalid'
}

// 👉 Alpha Validator
export const alphaValidator = (value: unknown) => {
  if (isEmpty(value)) return true

  return (
    /^[A-Z]*$/i.test(String(value)) ||
    'The Alpha field may only contain alphabetic characters'
  )
}

// 👉 URL Validator
export const urlValidator = (value: unknown) => {
  if (isEmpty(value)) return true

  const re =
    /^(http[s]?:\/\/){0,1}(www\.){0,1}[a-zA-Z0-9\.\-]+\.[a-zA-Z]{2,5}[\.]{0,1}/

  return re.test(String(value)) || 'URL is invalid'
}

// 👉 Length Validator
export const lengthValidator = (value: unknown, length: number) => {
  if (isEmpty(value)) return true

  return (
    String(value).length === length ||
    `The Min Character field must be at least ${length} characters`
  )
}

// 👉 Max Length Validator
export const maxLengthValidator = (value: unknown, length: number) => {
  if (String(value).length <= length) return true

  return `Dieses Feld darf maximal ${length} Zeichen haben`
}

// 👉 Alpha-dash Validator
export const alphaDashValidator = (value: unknown) => {
  if (isEmpty(value)) return true

  const valueAsString = String(value)

  return /^[0-9A-Z_-]*$/i.test(valueAsString) || 'All Character are not valid'
}

// 👉 Date Validator

export const dateValidator = (value: unknown) => {
  if (isEmpty(value)) return true

  return (
    !Number.isNaN(Date.parse(String(value))) ||
    'Dieses Feld muss ausgefüllt werden'
  )
}

export const dateRangeValidator = (
  startDate: Date | null | undefined,
  endDate: Date | null | undefined,
  errorMessage: string,
) => {
  return (value: Date) => {
    if (
      !startDate ||
      !endDate ||
      isNaN(startDate.getTime()) ||
      isNaN(endDate.getTime())
    ) {
      return true
    }

    const start = new Date(startDate)
    start.setHours(0, 0, 0, 0)

    const end = new Date(endDate)
    end.setHours(0, 0, 0, 0)

    if (start > end) {
      return errorMessage
    }

    return true
  }
}
export const futureDateValidator = (value: Date | string) => {
  const inputDate = new Date(value)
  inputDate.setHours(0, 0, 0, 0)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return (
    inputDate >= today || 'Das Datum darf nicht in der Vergangenheit liegen'
  )
}

export const registrationDateValidator = (
  registrationEndDate: Date,
  startDate: Date,
  errorMessage: string,
) => {
  return (value: Date) => {
    if (registrationEndDate && startDate && registrationEndDate > startDate) {
      return errorMessage
    }
    return true
  }
}
