import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/authStore'

export function useInactivityTimer(timeoutMinutes = 60) {
  const authStore = useAuthStore()
  const timer = ref<number | null>(null)
  const timeoutMs = timeoutMinutes * 60 * 1000 // Convert minutes to milliseconds

  // Function to reset the timer
  const resetTimer = () => {
    if (timer.value) {
      window.clearTimeout(timer.value)
    }
    
    // Set a new timeout
    timer.value = window.setTimeout(() => {
      console.log(`User inactive for ${timeoutMinutes} minutes, logging out`)
      authStore.signOut()
    }, timeoutMs)
  }

  // Function to track user activity
  const trackActivity = () => {
    resetTimer()
  }

  // Setup event listeners
  const setupEventListeners = () => {
    // Only setup if user is logged in
    if (!authStore.isLoggedIn) return

    // Track various user activities
    window.addEventListener('mousemove', trackActivity)
    window.addEventListener('mousedown', trackActivity)
    window.addEventListener('keypress', trackActivity)
    window.addEventListener('touchmove', trackActivity)
    window.addEventListener('scroll', trackActivity)
    
    // Initial timer setup
    resetTimer()
  }

  // Remove event listeners
  const cleanupEventListeners = () => {
    if (timer.value) {
      window.clearTimeout(timer.value)
      timer.value = null
    }

    window.removeEventListener('mousemove', trackActivity)
    window.removeEventListener('mousedown', trackActivity)
    window.removeEventListener('keypress', trackActivity)
    window.removeEventListener('touchmove', trackActivity)
    window.removeEventListener('scroll', trackActivity)
  }

  onMounted(() => {
    setupEventListeners()
  })

  onUnmounted(() => {
    cleanupEventListeners()
  })

  return {
    resetTimer,
    setupEventListeners,
    cleanupEventListeners
  }
}
