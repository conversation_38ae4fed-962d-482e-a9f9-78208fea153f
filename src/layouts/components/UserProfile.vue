<script setup lang="ts">
  import router from '@/router'
  import { computed } from 'vue'
  import { useAuthStore } from '@/stores/authStore'
  import { useUsersStore } from '@/stores/usersStore'
  import { useCompanyStore } from '@/stores/companyStore'
  import userProfile from '@/assets/images/avatars/avatar-0.png'

  const authStore = useAuthStore()
  const usersStore = useUsersStore()
  const companyStore = useCompanyStore()

  const signOut = async () => {
    // The signOut method in authStore will show the fullscreen loader
    const success = await authStore.signOut()

    // Only redirect if sign out was successful
    // This ensures we don't navigate away before the sign-out process is complete
    if (success) {
      // Small delay to ensure the loader has time to hide properly
      setTimeout(async () => {
        await router.push('/login')
      }, 300)
    }
  }

  const isSuper = computed(() => authStore.isSuperUser || authStore.isFGAdmin)

  const openDialog = ref(false)

  const switchCompany = () => {
    openDialog.value = true
  }

  const avatarImageUrl = computed(
    () => usersStore.activeUser?.avatarImageUrl || userProfile,
  )

  const currentUserName = computed(() => authStore.user?.displayName || '')
</script>

<template>
  <div>
    <CompanySwitchDialog
      v-if="!isSuper"
      :open="openDialog"
      @onClose="() => (openDialog = false)"
    />
    <VBadge
      dot
      location="bottom right"
      offset-x="3"
      offset-y="3"
      bordered
      color="success"
    >
      <VAvatar class="cursor-pointer" color="primary" variant="tonal">
        <VImg :src="avatarImageUrl" />

        <!-- SECTION Menu -->
        <VMenu
          activator="parent"
          width="280"
          location="bottom end"
          offset="14px"
        >
          <VList>
            <!-- 👉 User Avatar & Name -->
            <VListItem>
              <template #prepend>
                <VListItemAction start>
                  <VBadge
                    dot
                    location="bottom right"
                    offset-x="3"
                    offset-y="3"
                    color="success"
                  >
                    <VAvatar color="primary" variant="tonal">
                      <VImg :src="avatarImageUrl" />
                    </VAvatar>
                  </VBadge>
                </VListItemAction>
              </template>

              <VListItemTitle class="font-weight-semibold">
                {{ currentUserName }}
              </VListItemTitle>
              <VListItemSubtitle>
                <v-icon icon="tabler-building" />
                {{ companyStore.company?.name }}
              </VListItemSubtitle>
            </VListItem>

            <!-- Divider -->
            <VDivider class="my-2" />

            <!-- 👉 Logout -->
            <VListItem @click="switchCompany">
              <template #prepend>
                <VIcon class="me-2" icon="tabler-refresh" size="22" />
              </template>

              <VListItemTitle>Unternehmen wechseln</VListItemTitle>
            </VListItem>
            <VListItem to="/login">
              <template #prepend>
                <VIcon class="me-2" icon="tabler-logout" size="22" />
              </template>
              <VListItemTitle @click="signOut"> Logout </VListItemTitle>
            </VListItem>
          </VList>
        </VMenu>
        <!-- !SECTION -->
      </VAvatar>
    </VBadge>
  </div>
</template>
