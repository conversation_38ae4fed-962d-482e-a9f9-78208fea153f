<script setup lang="ts">
  import { useTheme } from 'vuetify'
  import { useThemeConfig } from '@core/composable/useThemeConfig'
  import { hexToRgb } from '@layouts/utils'
  import { useAppStore } from '@/stores/appStore'
  import { useAuthStore } from '@/stores/authStore'
  import { useInactivityTimer } from '@core/composable/useInactivityTimer'
  import { computed, watch } from 'vue'

  const {
    syncInitialLoaderTheme,
    syncVuetifyThemeWithTheme: syncConfigThemeWithVuetifyTheme,
    isAppRtl,
  } = useThemeConfig()

  const { global } = useTheme()

  // ℹ️ Sync current theme with initial loader theme
  syncInitialLoaderTheme()
  syncConfigThemeWithVuetifyTheme()

  const appStore = useAppStore()
  const snackbar = computed(() => appStore.showSnackbar)
  const appLoader = computed(() => appStore.showLoader)
  const snackMsg = computed(() => appStore.snackMsg)

  // Set up inactivity timer (60 minutes by default)
  const authStore = useAuthStore()
  const { setupEventListeners, cleanupEventListeners } = useInactivityTimer()

  // Watch for changes in login state to setup/cleanup event listeners
  watch(
    () => authStore.isLoggedIn,
    (isLoggedIn) => {
      if (isLoggedIn) {
        // User is logged in, setup inactivity tracking
        setupEventListeners()
      } else {
        // User is logged out, cleanup event listeners
        cleanupEventListeners()
      }
    },
    { immediate: true } // Check immediately on component creation
  )
</script>

<template>
  <v-snackbar v-model="snackbar" location="top">
    {{ snackMsg }}
    <template v-slot:actions>
      <v-btn color="primary" variant="text" @click="snackbar = false">
        schließen
      </v-btn>
    </template>
  </v-snackbar>
  <v-progress-linear
    color="primary"
    v-if="appLoader"
    model-value="100"
    indeterminate
    rounded
  ></v-progress-linear>
  <VLocaleProvider :rtl="isAppRtl">
    <!-- ℹ️ This is required to set the background color of active nav link based on currently active global theme's primary -->
    <VApp
      :style="`--v-global-theme-primary: ${hexToRgb(
        global.current.value.colors.primary,
      )}`"
    >
      <RouterView />
    </VApp>
  </VLocaleProvider>
</template>
