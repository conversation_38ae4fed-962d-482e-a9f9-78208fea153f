import gql from 'graphql-tag';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
};

export enum ActionState {
  Bookmarked = 'BOOKMARKED',
  Deleted = 'DELETED',
  Disliked = 'DISLIKED',
  Liked = 'LIKED',
  Matched = 'MATCHED'
}

export type Address = {
  __typename?: 'Address';
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  line1?: Maybe<Scalars['String']['output']>;
  line2?: Maybe<Scalars['String']['output']>;
  postal_code?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
};

export type Agora = {
  __typename?: 'Agora';
  token: Scalars['String']['output'];
  uid: Scalars['String']['output'];
};

export type Applicant = {
  __typename?: 'Applicant';
  applicantDocuments?: Maybe<Array<ApplicantDocument>>;
  availableFrom?: Maybe<Scalars['DateTime']['output']>;
  birthDate?: Maybe<Scalars['DateTime']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  deviceTokens?: Maybe<Array<DeviceToken>>;
  environment?: Maybe<Scalars['Float']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  graduation?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  jobAction?: Maybe<Array<JobAction>>;
  jobsFilter?: Maybe<JobsFilter>;
  lastActive?: Maybe<Scalars['DateTime']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  personality?: Maybe<Scalars['Float']['output']>;
  phoneNumber?: Maybe<Scalars['String']['output']>;
  profileImageUrl?: Maybe<Scalars['String']['output']>;
  receiveNotifications?: Maybe<Scalars['Boolean']['output']>;
  schoolName?: Maybe<Scalars['String']['output']>;
  strengths?: Maybe<Array<Scalars['String']['output']>>;
  subjects?: Maybe<Array<Scalars['String']['output']>>;
  userId?: Maybe<Scalars['String']['output']>;
  weaknesses?: Maybe<Array<Scalars['String']['output']>>;
};

export type ApplicantDocument = {
  __typename?: 'ApplicantDocument';
  applicant: Applicant;
  documentPreviewUrl: Scalars['String']['output'];
  /** ID */
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  storage: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type Appointment = {
  __typename?: 'Appointment';
  applicant: Applicant;
  applicantId: Scalars['String']['output'];
  applicantIsNew: Scalars['Boolean']['output'];
  chatRoomId: Scalars['String']['output'];
  companyIsNew: Scalars['Boolean']['output'];
  contactPersonTimeslot: ContactPersonTimeslot;
  contactPersonTimeslotId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  rejectReason?: Maybe<Scalars['String']['output']>;
  reservationDate: Scalars['DateTime']['output'];
  status: AppointmentStatus;
};

export type AppointmentFilterInput = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  companyId?: InputMaybe<Scalars['String']['input']>;
  contactPersonId?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['DateTime']['input']>;
  fairId?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<AppointmentStatus>;
  timeRange?: InputMaybe<TimeRangeInput>;
};

export type AppointmentPaginated = {
  __typename?: 'AppointmentPaginated';
  count?: Maybe<Scalars['Float']['output']>;
  items: Array<Appointment>;
};

export enum AppointmentStatus {
  Canceled = 'CANCELED',
  Confirmed = 'CONFIRMED',
  Rejected = 'REJECTED',
  Requested = 'REQUESTED'
}

export type BillingDetails = {
  __typename?: 'BillingDetails';
  address?: Maybe<Address>;
  email?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
};

export type CancelSubscriptionResponseDto = {
  __typename?: 'CancelSubscriptionResponseDto';
  application_fee_percent: Scalars['Float']['output'];
  cancel_at_period_end: Scalars['Boolean']['output'];
  canceled_at: Scalars['Float']['output'];
  created: Scalars['Float']['output'];
  current_period_end: Scalars['Float']['output'];
  current_period_start: Scalars['Float']['output'];
  customer: Scalars['String']['output'];
  ended_at: Scalars['Float']['output'];
  id: Scalars['String']['output'];
  object: Scalars['String']['output'];
  start: Scalars['Float']['output'];
  status: Scalars['String']['output'];
};

export type Card = {
  __typename?: 'Card';
  brand?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  exp_month?: Maybe<Scalars['Float']['output']>;
  exp_year?: Maybe<Scalars['Float']['output']>;
  fingerprint: Scalars['String']['output'];
  funding?: Maybe<Scalars['String']['output']>;
  generated_from?: Maybe<Scalars['String']['output']>;
  last4?: Maybe<Scalars['String']['output']>;
  wallet?: Maybe<Scalars['String']['output']>;
};

export type Charges = {
  __typename?: 'Charges';
  data: Array<Scalars['String']['output']>;
  has_more: Scalars['Boolean']['output'];
  object: Scalars['String']['output'];
  total_count: Scalars['Float']['output'];
  url: Scalars['String']['output'];
};

export type ChatRoom = {
  __typename?: 'ChatRoom';
  appointment?: Maybe<Appointment>;
  companyUsers?: Maybe<Array<CompanyUser>>;
  id: Scalars['String']['output'];
  jobAction?: Maybe<JobAction>;
  messages: Array<Message>;
  status?: Maybe<Scalars['String']['output']>;
};

export type ChatRoomCriteriaInput = {
  appointmentId?: InputMaybe<Scalars['String']['input']>;
  jobActionId?: InputMaybe<Scalars['String']['input']>;
};

export type ClaimsResponse = {
  __typename?: 'ClaimsResponse';
  claims?: Maybe<FirebaseUserDto>;
  error?: Maybe<ErrorType>;
};

export type Company = {
  __typename?: 'Company';
  address: Scalars['String']['output'];
  city: Scalars['String']['output'];
  companyUserId?: Maybe<Scalars['String']['output']>;
  companyUsers?: Maybe<Array<CompanyUser>>;
  country: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  detailContent?: Maybe<Scalars['String']['output']>;
  dynamicLink?: Maybe<Scalars['String']['output']>;
  foundingYear?: Maybe<Scalars['Float']['output']>;
  headerImageUrl?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isFairManaged?: Maybe<Scalars['Boolean']['output']>;
  isInFair?: Maybe<Scalars['Boolean']['output']>;
  jobAdvert?: Maybe<Array<JobAdvert>>;
  latitude?: Maybe<Scalars['Float']['output']>;
  logoImageUrl?: Maybe<Scalars['String']['output']>;
  longitude?: Maybe<Scalars['Float']['output']>;
  name: Scalars['String']['output'];
  stripeCustomerId?: Maybe<Scalars['String']['output']>;
  totalEmployees?: Maybe<Scalars['Float']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type CompanyFairContactPerson = {
  __typename?: 'CompanyFairContactPerson';
  ContactPersonTimeslot: Array<ContactPersonTimeslot>;
  companyFairParticipation: CompanyFairParticipation;
  companyFairParticipationId: Scalars['String']['output'];
  contactPerson: ContactPerson;
  contactPersonId: Scalars['String']['output'];
  id: Scalars['String']['output'];
};

export type CompanyFairJob = {
  __typename?: 'CompanyFairJob';
  companyFairParticipation: CompanyFairParticipation;
  companyFairParticipationId: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  fairJob: FairJob;
  fairJobId: Scalars['String']['output'];
  id: Scalars['String']['output'];
};

export type CompanyFairParticipation = {
  __typename?: 'CompanyFairParticipation';
  categories?: Maybe<Array<JobCategory>>;
  company: Company;
  companyFairContactPersons?: Maybe<Array<CompanyFairContactPerson>>;
  companyFairJobs?: Maybe<Array<CompanyFairJob>>;
  companyId: Scalars['String']['output'];
  fair: Fair;
  fairId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  partnerLinks?: Maybe<Array<PartnerLink>>;
};

export type CompanyUser = {
  __typename?: 'CompanyUser';
  activeCompanyId?: Maybe<Scalars['String']['output']>;
  avatarImageUrl?: Maybe<Scalars['String']['output']>;
  companies: Array<Company>;
  createdAt: Scalars['DateTime']['output'];
  email?: Maybe<Scalars['String']['output']>;
  /** Company User ID */
  id: Scalars['String']['output'];
  jobAdverts: Array<JobAdvert>;
  name?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
  user: User;
  userId: Scalars['String']['output'];
  userRights: Array<UserRight>;
};

export type ConfirmPaymentIntentInput = {
  /** Payment Intent ID */
  paymentIntentId: Scalars['String']['input'];
  /** Payment Method ID */
  paymentMethodId: Scalars['String']['input'];
};

export type ContactPerson = {
  __typename?: 'ContactPerson';
  company: Company;
  companyFairContactPersons: Array<CompanyFairContactPerson>;
  companyId: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  position?: Maybe<Scalars['String']['output']>;
};

export type ContactPersonTimeslot = {
  __typename?: 'ContactPersonTimeslot';
  Appointment: Appointment;
  available: Scalars['Boolean']['output'];
  companyFairContactPerson: CompanyFairContactPerson;
  companyFairContactPersonId: Scalars['String']['output'];
  endTime: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  startTime: Scalars['DateTime']['output'];
};

export type CreateAgoraInput = {
  jobActionId: Scalars['String']['input'];
};

export type CreateApplicantDocumentInput = {
  documentPreviewUrl: Scalars['String']['input'];
  name: Scalars['String']['input'];
  storage: Scalars['String']['input'];
  url: Scalars['String']['input'];
};

export type CreateApplicantInput = {
  availableFrom?: InputMaybe<Scalars['DateTime']['input']>;
  birthDate?: InputMaybe<Scalars['DateTime']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  environment?: InputMaybe<Scalars['Int']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  graduation?: InputMaybe<Scalars['String']['input']>;
  lastActive?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  personality?: InputMaybe<Scalars['Int']['input']>;
  profileImageUrl?: InputMaybe<Scalars['String']['input']>;
  receiveNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  schoolName?: InputMaybe<Scalars['String']['input']>;
  strengths?: InputMaybe<Array<Scalars['String']['input']>>;
  subjects?: InputMaybe<Array<Scalars['String']['input']>>;
  weaknesses?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CreateAppointmentInput = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  contactPersonTimeslotId: Scalars['String']['input'];
  rejectReason?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<AppointmentStatus>;
};

export type CreateChatRoomInput = {
  status?: InputMaybe<Scalars['String']['input']>;
};

export type CreateCheckoutSessionInput = {
  /** Advert ID from frontend */
  advertId: Scalars['String']['input'];
  /** Advert Title ID from frontend */
  advertTitle: Scalars['String']['input'];
  /** User email from frontend */
  email: Scalars['String']['input'];
  /** Price ID from frontend */
  priceId: Scalars['String']['input'];
};

export type CreateCompanyDynamicLinkInput = {
  companyId: Scalars['String']['input'];
};

export type CreateCompanyFairContactPersonInput = {
  companyFairParticipationId: Scalars['String']['input'];
  contactPersonId: Scalars['String']['input'];
};

export type CreateCompanyFairJobInput = {
  companyFairParticipationId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  fairJobId: Scalars['String']['input'];
};

export type CreateCompanyFairParticipationInput = {
  categoryIds: Array<Scalars['String']['input']>;
  companyId: Scalars['String']['input'];
  fairId: Scalars['String']['input'];
  partnerLinkIds?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CreateCompanyInput = {
  address: Scalars['String']['input'];
  city: Scalars['String']['input'];
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  country: Scalars['String']['input'];
  detailContent?: InputMaybe<Scalars['String']['input']>;
  dynamicLink?: InputMaybe<Scalars['String']['input']>;
  foundingYear?: InputMaybe<Scalars['Float']['input']>;
  headerImageUrl?: InputMaybe<Scalars['String']['input']>;
  isFairManaged?: InputMaybe<Scalars['Boolean']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  logoImageUrl?: InputMaybe<Scalars['String']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  name: Scalars['String']['input'];
  stripeCustomerId?: InputMaybe<Scalars['String']['input']>;
  totalEmployees?: InputMaybe<Scalars['Float']['input']>;
};

export type CreateCompanyUserInput = {
  activeCompanyId?: InputMaybe<Scalars['String']['input']>;
  avatarImageUrl?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type CreateContactPersonInput = {
  companyId: Scalars['String']['input'];
  email?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
};

export type CreateContactPersonTimeslotInput = {
  available?: InputMaybe<Scalars['Boolean']['input']>;
  companyFairContactPersonId: Scalars['String']['input'];
  endTime: Scalars['DateTime']['input'];
  startTime: Scalars['DateTime']['input'];
};

export type CreateDeviceTokenInput = {
  token: Scalars['String']['input'];
};

export type CreateEmailNotificationInput = {
  emailBody?: InputMaybe<Scalars['String']['input']>;
  emailSubject?: InputMaybe<Scalars['String']['input']>;
};

export type CreateFairDayInput = {
  day: Scalars['DateTime']['input'];
  endTime: Scalars['DateTime']['input'];
  fairId?: InputMaybe<Scalars['String']['input']>;
  startTime: Scalars['DateTime']['input'];
};

export type CreateFairInput = {
  city: Scalars['String']['input'];
  contactPersonEmail?: InputMaybe<Scalars['String']['input']>;
  contactPersonName?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  endDate: Scalars['DateTime']['input'];
  location: Scalars['String']['input'];
  locationName?: InputMaybe<Scalars['String']['input']>;
  logoImageUrl?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  publisherLogoImageUrl?: InputMaybe<Scalars['String']['input']>;
  publisherName?: InputMaybe<Scalars['String']['input']>;
  registrationEndDate: Scalars['DateTime']['input'];
  registrationStartDate: Scalars['DateTime']['input'];
  startDate: Scalars['DateTime']['input'];
  status?: InputMaybe<Scalars['String']['input']>;
};

export type CreateFairJobInput = {
  title: Scalars['String']['input'];
};

export type CreateFgadminInput = {
  avatarImageUrl?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type CreateJobActionHistoryInput = {
  newState: Scalars['String']['input'];
  prevState: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type CreateJobActionInput = {
  applicantIsNew?: InputMaybe<Scalars['Boolean']['input']>;
  companyIsNew?: InputMaybe<Scalars['Boolean']['input']>;
  declineReason?: InputMaybe<Scalars['String']['input']>;
  deletedFromApplicant?: InputMaybe<Scalars['Boolean']['input']>;
  deletedFromCompany?: InputMaybe<Scalars['Boolean']['input']>;
  state: ActionState;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type CreateJobAdDynamicLinkInput = {
  jobAdvertId: Scalars['String']['input'];
};

export type CreateJobAdvertInput = {
  activeFromDate?: InputMaybe<Scalars['DateTime']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  approved?: InputMaybe<Scalars['Boolean']['input']>;
  city: Scalars['String']['input'];
  companyName?: InputMaybe<Scalars['String']['input']>;
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  declineReason?: InputMaybe<Scalars['String']['input']>;
  description: Scalars['String']['input'];
  detailDescription: Scalars['String']['input'];
  district: Scalars['String']['input'];
  educationDuration?: InputMaybe<Scalars['Float']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  gehalt: Array<Scalars['Float']['input']>;
  headerImageUrl?: InputMaybe<Scalars['String']['input']>;
  holidayDays?: InputMaybe<Scalars['Float']['input']>;
  imageUrl?: InputMaybe<Scalars['String']['input']>;
  impressions?: InputMaybe<Scalars['Float']['input']>;
  isDeclined?: InputMaybe<Scalars['Boolean']['input']>;
  isDeleted?: InputMaybe<Scalars['Boolean']['input']>;
  isDraft?: InputMaybe<Scalars['Boolean']['input']>;
  jobCategoryId?: InputMaybe<Scalars['String']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  startDate: Scalars['DateTime']['input'];
  title: Scalars['String']['input'];
  type: Scalars['String']['input'];
  workHours?: InputMaybe<Scalars['Float']['input']>;
};

export type CreateJobCategoryInput = {
  imageUrl: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type CreateJobsFilterInput = {
  categories: Array<Scalars['String']['input']>;
  currentLocation?: InputMaybe<Scalars['String']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  type?: InputMaybe<Array<JobAdvertType>>;
};

export type CreateMessageInput = {
  authorName: Scalars['String']['input'];
  content: Scalars['String']['input'];
  deletedAt?: InputMaybe<Scalars['DateTime']['input']>;
  deletedBy?: InputMaybe<Scalars['String']['input']>;
  deletedById?: InputMaybe<Scalars['String']['input']>;
  isApplicant?: InputMaybe<Scalars['Boolean']['input']>;
  isCompany?: InputMaybe<Scalars['Boolean']['input']>;
  isDeleted?: InputMaybe<Scalars['Boolean']['input']>;
  isDelivered?: InputMaybe<Scalars['Boolean']['input']>;
  isSeen?: InputMaybe<Scalars['Boolean']['input']>;
  isSent?: InputMaybe<Scalars['Boolean']['input']>;
};

export type CreateNotificationInput = {
  isNew?: InputMaybe<Scalars['Boolean']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type CreatePartnerLinkInput = {
  name: Scalars['String']['input'];
  url: Scalars['String']['input'];
};

export type CreatePaymentIntentInput = {
  /** Payment amount */
  amount: Scalars['Float']['input'];
  /** Payment currency */
  currency?: InputMaybe<Scalars['String']['input']>;
  /** Payment customer */
  customer?: InputMaybe<Scalars['String']['input']>;
};

export type CreatePortalSessionInput = {
  /** Stripe Customer ID from frontend */
  customerId: Scalars['String']['input'];
};

export type CreatePushNotificationInput = {
  actionUrl?: InputMaybe<Scalars['String']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type CreateSessionResponseDto = {
  __typename?: 'CreateSessionResponseDto';
  url: Scalars['String']['output'];
};

export type CreateStaleImageInput = {
  url: Scalars['String']['input'];
};

export type CreateStripeCustomerResponseDto = {
  __typename?: 'CreateStripeCustomerResponseDto';
  address?: Maybe<Scalars['String']['output']>;
  balance?: Maybe<Scalars['Float']['output']>;
  created?: Maybe<Scalars['DateTime']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  default_source?: Maybe<Scalars['String']['output']>;
  delinquent?: Maybe<Scalars['Boolean']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  discount?: Maybe<Scalars['String']['output']>;
  email: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
  object?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
};

export type CreateStripeSubscriptionInput = {
  advertTitle?: InputMaybe<Scalars['String']['input']>;
  companyId: Scalars['String']['input'];
  couponId?: InputMaybe<Scalars['String']['input']>;
  customerId?: InputMaybe<Scalars['String']['input']>;
  jobAdvertId?: InputMaybe<Scalars['String']['input']>;
  jobAdverts?: InputMaybe<Array<Scalars['String']['input']>>;
  percent_off?: InputMaybe<Scalars['Float']['input']>;
  priceId: Scalars['String']['input'];
  promoCode?: InputMaybe<Scalars['String']['input']>;
};

export type CreateStripeSubscriptionResponseDto = {
  __typename?: 'CreateStripeSubscriptionResponseDto';
  cancel_at_period_end?: Maybe<Scalars['Boolean']['output']>;
  hosted_invoice_url?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  invoice_pdf?: Maybe<Scalars['String']['output']>;
  latest_invoice: StripeInvoiceResponseDto;
};

export type CreateSubscriptionInput = {
  amountTotal: Scalars['Float']['input'];
  checkoutSessionId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  expiresAt: Scalars['DateTime']['input'];
  invoiceId: Scalars['String']['input'];
  isActive?: Scalars['Boolean']['input'];
  paymentStatus: Scalars['String']['input'];
  percent_off?: InputMaybe<Scalars['Float']['input']>;
  plan: Scalars['String']['input'];
  status?: InputMaybe<Scalars['String']['input']>;
  stripeCustomerId: Scalars['String']['input'];
  stripeSubscriptionId: Scalars['String']['input'];
};

export type CreateSuperUserInput = {
  avatarImageUrl?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type CreateTimeslotInput = {
  endTime: Scalars['DateTime']['input'];
  fairId: Scalars['String']['input'];
  startTime: Scalars['DateTime']['input'];
};

export type CreateUpdateJobActionInput = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  applicantIsNew?: InputMaybe<Scalars['Boolean']['input']>;
  companyIsNew?: InputMaybe<Scalars['Boolean']['input']>;
  declineReason?: InputMaybe<Scalars['String']['input']>;
  deletedFromApplicant?: InputMaybe<Scalars['Boolean']['input']>;
  deletedFromCompany?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  jobAdvertId?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<ActionState>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type CreateUserRightInput = {
  companyAdmin?: InputMaybe<Scalars['Boolean']['input']>;
  createJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  createUser?: InputMaybe<Scalars['Boolean']['input']>;
  deleteJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  editCompany?: InputMaybe<Scalars['Boolean']['input']>;
  editJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  superAdmin?: InputMaybe<Scalars['Boolean']['input']>;
  updateJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  viewApplicants?: InputMaybe<Scalars['Boolean']['input']>;
  viewJobAd?: InputMaybe<Scalars['Boolean']['input']>;
};

export type CustomerIdInput = {
  /** Stripe Customer ID */
  customerId: Scalars['String']['input'];
};

export type DataObj = {
  __typename?: 'DataObj';
  billing_details?: Maybe<BillingDetails>;
  card?: Maybe<Card>;
  created?: Maybe<Scalars['Float']['output']>;
  customer: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  livemode?: Maybe<Scalars['Boolean']['output']>;
  object?: Maybe<Scalars['String']['output']>;
  redaction?: Maybe<Scalars['String']['output']>;
  sepa_debit?: Maybe<Sepa>;
  type: Scalars['String']['output'];
};

export type DeviceToken = {
  __typename?: 'DeviceToken';
  applicant: Applicant;
  id: Scalars['String']['output'];
  token?: Maybe<Scalars['String']['output']>;
};

export type DynamicLink = {
  __typename?: 'DynamicLink';
  /** Dynamic Link */
  url: Scalars['String']['output'];
};

export type EmailNotification = {
  __typename?: 'EmailNotification';
  createdAt: Scalars['DateTime']['output'];
  emailBody?: Maybe<Scalars['String']['output']>;
  emailSubject?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type ErrorType = {
  __typename?: 'ErrorType';
  code?: Maybe<Scalars['String']['output']>;
  message: Scalars['String']['output'];
};

export type Fair = {
  __typename?: 'Fair';
  city: Scalars['String']['output'];
  companyFairParticipations: Array<CompanyFairParticipation>;
  contactPersonEmail?: Maybe<Scalars['String']['output']>;
  contactPersonName?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  endDate: Scalars['DateTime']['output'];
  fairDays?: Maybe<Array<FairDay>>;
  id: Scalars['String']['output'];
  location: Scalars['String']['output'];
  locationName?: Maybe<Scalars['String']['output']>;
  logoImageUrl?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  publisherLogoImageUrl?: Maybe<Scalars['String']['output']>;
  publisherName?: Maybe<Scalars['String']['output']>;
  registrationEndDate: Scalars['DateTime']['output'];
  registrationStartDate: Scalars['DateTime']['output'];
  standardTimeslots: Array<Timeslot>;
  startDate: Scalars['DateTime']['output'];
  status?: Maybe<FairStatus>;
};

export type FairDay = {
  __typename?: 'FairDay';
  createdAt: Scalars['DateTime']['output'];
  day: Scalars['DateTime']['output'];
  endTime: Scalars['DateTime']['output'];
  fair: Fair;
  fairId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  startTime: Scalars['DateTime']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type FairJob = {
  __typename?: 'FairJob';
  companyFairJobs: Array<CompanyFairJob>;
  id: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type FairStats = {
  __typename?: 'FairStats';
  appointments: Scalars['Int']['output'];
  chatRooms: Scalars['Int']['output'];
  companies: Scalars['Int']['output'];
  fairs: Scalars['Int']['output'];
};

export enum FairStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

export type Fgadmin = {
  __typename?: 'Fgadmin';
  avatarImageUrl?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
  user: User;
  userId: Scalars['String']['output'];
};

export type FilterOptionsResponse = {
  __typename?: 'FilterOptionsResponse';
  applicants: Array<IdTextPair>;
  companies: Array<IdTextPair>;
  contactPersons: Array<IdTextPair>;
  fairs: Array<IdTextPair>;
};

export type FirebaseCreateUserDto = {
  avatarImageUrl?: InputMaybe<Scalars['String']['input']>;
  disabled?: InputMaybe<Scalars['Boolean']['input']>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  email: Scalars['String']['input'];
  emailVerified?: InputMaybe<Scalars['Boolean']['input']>;
  password: Scalars['String']['input'];
  phoneNumber?: InputMaybe<Scalars['String']['input']>;
};

export type FirebaseUserDto = {
  __typename?: 'FirebaseUserDto';
  applicantId?: Maybe<Scalars['String']['output']>;
  aud?: Maybe<Scalars['String']['output']>;
  auth_time?: Maybe<Scalars['Float']['output']>;
  bridgeUserId?: Maybe<Scalars['String']['output']>;
  companyId?: Maybe<Scalars['String']['output']>;
  companyUserId?: Maybe<Scalars['String']['output']>;
  createJobAd?: Maybe<Scalars['Boolean']['output']>;
  createUser?: Maybe<Scalars['Boolean']['output']>;
  editCompany?: Maybe<Scalars['Boolean']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  email_verified?: Maybe<Scalars['Boolean']['output']>;
  exp?: Maybe<Scalars['Float']['output']>;
  iat?: Maybe<Scalars['Float']['output']>;
  iss?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  sub?: Maybe<Scalars['String']['output']>;
  superAdmin?: Maybe<Scalars['Boolean']['output']>;
  uid?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
};

export type IdTextPair = {
  __typename?: 'IdTextPair';
  id: Scalars['String']['output'];
  text: Scalars['String']['output'];
};

export type InvoiceIdInput = {
  /** Stripe Invoice ID */
  invoiceId: Scalars['String']['input'];
};

export type Issuer = {
  __typename?: 'Issuer';
  type: Scalars['String']['output'];
};

export type JobAction = {
  __typename?: 'JobAction';
  applicant: Applicant;
  applicantIsNew?: Maybe<Scalars['Boolean']['output']>;
  chatRoomId?: Maybe<Scalars['String']['output']>;
  companyIsNew?: Maybe<Scalars['Boolean']['output']>;
  declineReason?: Maybe<Scalars['String']['output']>;
  deletedFromApplicant?: Maybe<Scalars['Boolean']['output']>;
  deletedFromCompany?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  jobAdvert: JobAdvert;
  state: ActionState;
  status?: Maybe<Scalars['String']['output']>;
};

export type JobActionHistory = {
  __typename?: 'JobActionHistory';
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  jobAction: JobAction;
  newState?: Maybe<Scalars['String']['output']>;
  prevState?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type JobAdvert = {
  __typename?: 'JobAdvert';
  _count?: Maybe<JobAdvertStats>;
  activeFromDate?: Maybe<Scalars['DateTime']['output']>;
  address?: Maybe<Scalars['String']['output']>;
  approved?: Maybe<Scalars['Boolean']['output']>;
  categories?: Maybe<Array<JobCategory>>;
  city?: Maybe<Scalars['String']['output']>;
  company: Company;
  companyId?: Maybe<Scalars['String']['output']>;
  companyUserId?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['DateTime']['output']>;
  declineReason?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  detailDescription?: Maybe<Scalars['String']['output']>;
  district?: Maybe<Scalars['String']['output']>;
  dynamicLink?: Maybe<Scalars['String']['output']>;
  educationDuration?: Maybe<Scalars['Float']['output']>;
  gehalt?: Maybe<Array<Scalars['Float']['output']>>;
  headerImageUrl?: Maybe<Scalars['String']['output']>;
  holidayDays?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  isDeclined?: Maybe<Scalars['Boolean']['output']>;
  isDeleted?: Maybe<Scalars['Boolean']['output']>;
  isDraft?: Maybe<Scalars['Boolean']['output']>;
  jobAction?: Maybe<Array<JobAction>>;
  latitude?: Maybe<Scalars['Float']['output']>;
  longitude?: Maybe<Scalars['Float']['output']>;
  paused?: Maybe<Scalars['Boolean']['output']>;
  responsibleUsers?: Maybe<Array<CompanyUser>>;
  startDate?: Maybe<Scalars['DateTime']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptions?: Maybe<Array<StripeSubscription>>;
  title: Scalars['String']['output'];
  type?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  workHours?: Maybe<Scalars['Float']['output']>;
};

export type JobAdvertCount = {
  __typename?: 'JobAdvertCount';
  jobAdverts: Scalars['Int']['output'];
};

export type JobAdvertFilterOptionsInput = {
  categoryId?: InputMaybe<Scalars['String']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  type?: InputMaybe<Array<JobAdvertType>>;
};

export type JobAdvertStats = {
  __typename?: 'JobAdvertStats';
  bookmarks: Scalars['Int']['output'];
  impressions: Scalars['Int']['output'];
  likes: Scalars['Int']['output'];
  matches: Scalars['Int']['output'];
};

export enum JobAdvertType {
  Ausbildung = 'AUSBILDUNG',
  Praktikum = 'PRAKTIKUM'
}

export type JobCategory = {
  __typename?: 'JobCategory';
  _count: JobAdvertCount;
  id: Scalars['String']['output'];
  imageUrl: Scalars['String']['output'];
  jobAdverts: Array<JobAdvert>;
  jobsFilter: Array<JobsFilter>;
  name: Scalars['String']['output'];
};

export type JobsFilter = {
  __typename?: 'JobsFilter';
  applicant: Applicant;
  categories: Array<JobCategory>;
  currentLocation?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  latitude?: Maybe<Scalars['Float']['output']>;
  longitude?: Maybe<Scalars['Float']['output']>;
  radius?: Maybe<Scalars['Float']['output']>;
  type: Array<JobAdvertType>;
};

export type Lines = {
  __typename?: 'Lines';
  has_more: Scalars['Boolean']['output'];
  object: Scalars['String']['output'];
  total_count: Scalars['Float']['output'];
  url: Scalars['String']['output'];
};

export type LoginRequestDto = {
  token?: InputMaybe<Scalars['String']['input']>;
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  error?: Maybe<ErrorType>;
  user?: Maybe<FirebaseUserDto>;
};

export type Message = {
  __typename?: 'Message';
  authorName?: Maybe<Scalars['String']['output']>;
  content: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  deletedBy?: Maybe<Scalars['String']['output']>;
  deletedById?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isApplicant?: Maybe<Scalars['Boolean']['output']>;
  isCompany?: Maybe<Scalars['Boolean']['output']>;
  isDeleted?: Maybe<Scalars['Boolean']['output']>;
  isDelivered?: Maybe<Scalars['Boolean']['output']>;
  isSeen?: Maybe<Scalars['Boolean']['output']>;
  isSent?: Maybe<Scalars['Boolean']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  approveJobAdvert: JobAdvert;
  blockJobAdvert: JobAdvert;
  cancelSubscription: CancelSubscriptionResponseDto;
  cloneFair: Fair;
  confirmPaymentIntent: PaymentIntentResponseDto;
  createApplicant: Applicant;
  createApplicantDocument: ApplicantDocument;
  createAppointment: Appointment;
  createBulkCheckoutSession: CreateSessionResponseDto;
  createChatRoom: ChatRoom;
  createCheckoutSession: CreateSessionResponseDto;
  createCompany: Company;
  createCompanyByAdmin: Company;
  createCompanyDynamicLink: DynamicLink;
  createCompanyFairContactPerson: CompanyFairContactPerson;
  createCompanyFairJob: CompanyFairJob;
  createCompanyFairParticipation: CompanyFairParticipation;
  createCompanyUser: CompanyUser;
  createContactPerson: ContactPerson;
  createContactPersonTimeslot: ContactPersonTimeslot;
  createDeviceToken: DeviceToken;
  createFair: Fair;
  createFairDay: FairDay;
  createFairJob: FairJob;
  createFgadmin: Fgadmin;
  createFirebaseBridgeUser: CompanyUser;
  createJobAction: JobAction;
  createJobActionHistory: JobActionHistory;
  createJobAd: JobAdvert;
  createJobAdDynamicLink: DynamicLink;
  createJobCategory: JobCategory;
  createJobsFilter: JobsFilter;
  createMessage: Message;
  createNotification: Notification;
  createPartnerLink: PartnerLink;
  createPaymentIntent: PaymentIntentResponseDto;
  createPortalSession: CreateSessionResponseDto;
  createSetupCheckoutDetails: CreateSessionResponseDto;
  createStaleImage: StaleImage;
  createStripeCustomer: CreateStripeCustomerResponseDto;
  createStripeSubscription: CreateStripeSubscriptionResponseDto;
  createSubscription: StripeSubscription;
  createSuperUser: SuperUser;
  createTimeslot: Timeslot;
  createUserRights: UserRight;
  declineApplicant: JobAction;
  declineJobAction: JobAction;
  deleteImage: Scalars['Boolean']['output'];
  deleteNotification: Scalars['String']['output'];
  generateSubscriptionInvoice: CreateStripeSubscriptionResponseDto;
  login: LoginResponse;
  manageFair: Company;
  markAllAsRead: Scalars['String']['output'];
  markAsRead: Notification;
  pauseJobAdvert: JobAdvert;
  register: RegisterResponse;
  registerApplicant: RegisterResponse;
  removeAllStaleImages: Scalars['Int']['output'];
  removeApplicant: Applicant;
  removeApplicantDocument: ApplicantDocument;
  removeAppointment: Appointment;
  removeChatRoom: ChatRoom;
  removeCompany: Company;
  removeCompanyFairContactPerson: CompanyFairContactPerson;
  removeCompanyFairJob: CompanyFairJob;
  removeCompanyFairParticipation: CompanyFairParticipation;
  removeCompanyFromFair: CompanyFairParticipation;
  removeCompanyUser: CompanyUser;
  removeContactPerson: ContactPerson;
  removeContactPersonTimeslot: ContactPersonTimeslot;
  removeDeviceToken: DeviceToken;
  removeDeviceTokensByApplicantId: DeviceToken;
  removeFair: Fair;
  removeFairDay: FairDay;
  removeFairJob: FairJob;
  removeFgadmin: Fgadmin;
  removeJobAdvert: JobAdvert;
  removeJobCategory: JobCategory;
  removeJobsFilter: JobsFilter;
  removeMultipleStaleImages: Scalars['Int']['output'];
  removeMultipleTimeslots: Scalars['Int']['output'];
  removePartnerLink: PartnerLink;
  removeTimeslot: Timeslot;
  resetPassword: Scalars['String']['output'];
  resumeJobAdvert: JobAdvert;
  resumeSubscription: CancelSubscriptionResponseDto;
  retrieveStripeCoupon: StripeCouponResponseDto;
  retrieveStripePromoCode: StripePromoCodeResponseDto;
  retrieveSubscriptionInvoice: StripeInvoiceResponseDto;
  sendEmailNotification: Notification;
  sendPushNotification: Notification;
  sendSubscriptionInvoice: StripeInvoiceResponseDto;
  setApplicant: Applicant;
  setAppointmentAsOld: Appointment;
  setCustomUserClaims: ClaimsResponse;
  setDeviceToken: DeviceToken;
  setJobAction: JobAction;
  setJobsFilter: JobsFilter;
  subscribeToPremium: PaymentIntentResponseDto;
  updateApplicant: Applicant;
  updateApplicantDocument: ApplicantDocument;
  updateAppointment: Appointment;
  updateChatRoom: ChatRoom;
  updateCompany: Company;
  updateCompanyFairContactPerson: CompanyFairContactPerson;
  updateCompanyFairJob: CompanyFairJob;
  updateCompanyFairParticipation: CompanyFairParticipation;
  updateCompanyUser: CompanyUser;
  updateContactPerson: ContactPerson;
  updateContactPersonTimeslot: ContactPersonTimeslot;
  updateDeviceToken: DeviceToken;
  updateFair: Fair;
  updateFairDay: FairDay;
  updateFairJob: FairJob;
  updateFgadmin: Fgadmin;
  updateJobAction: JobAction;
  updateJobAdvert: JobAdvert;
  updateJobCategory: JobCategory;
  updateJobsFilter: JobsFilter;
  updateMessage: Message;
  updatePartnerLink: PartnerLink;
  updateStripeCustomer: CreateStripeCustomerResponseDto;
  updateSubscription: StripeSubscription;
  updateTimeslot: Timeslot;
  updateUserRights: UserRight;
  verifyCompanyUser: CompanyUser;
  verifyFGAdmin: Fgadmin;
  verifySuperUser: SuperUser;
};


export type MutationApproveJobAdvertArgs = {
  jobAdId: Scalars['String']['input'];
  superUserId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationBlockJobAdvertArgs = {
  declineReason: Scalars['String']['input'];
  jobAdId: Scalars['String']['input'];
  superUserId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCancelSubscriptionArgs = {
  subscriptionIdInput: SubscriptionIdInput;
};


export type MutationCloneFairArgs = {
  cloneFairInput: CreateFairInput;
  id: Scalars['String']['input'];
};


export type MutationConfirmPaymentIntentArgs = {
  confirmPaymentIntentInput: ConfirmPaymentIntentInput;
};


export type MutationCreateApplicantArgs = {
  createApplicantInput?: InputMaybe<CreateApplicantInput>;
};


export type MutationCreateApplicantDocumentArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  createApplicantDocumentInput: CreateApplicantDocumentInput;
};


export type MutationCreateAppointmentArgs = {
  createAppointmentInput: CreateAppointmentInput;
};


export type MutationCreateBulkCheckoutSessionArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  createCheckoutSessionInput: Array<CreateCheckoutSessionInput>;
  customer?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateChatRoomArgs = {
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  createChatRoomInput: CreateChatRoomInput;
  jobActionId: Scalars['String']['input'];
};


export type MutationCreateCheckoutSessionArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  createCheckoutSessionInput: CreateCheckoutSessionInput;
  customer?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateCompanyArgs = {
  companyInput: CreateCompanyInput;
};


export type MutationCreateCompanyByAdminArgs = {
  companyInput: CreateCompanyInput;
};


export type MutationCreateCompanyDynamicLinkArgs = {
  createDynamicLinkInput: CreateCompanyDynamicLinkInput;
};


export type MutationCreateCompanyFairContactPersonArgs = {
  createCompanyFairContactPersonInput: CreateCompanyFairContactPersonInput;
};


export type MutationCreateCompanyFairJobArgs = {
  createCompanyFairJobInput: CreateCompanyFairJobInput;
};


export type MutationCreateCompanyFairParticipationArgs = {
  createCompanyFairParticipationInput: CreateCompanyFairParticipationInput;
};


export type MutationCreateCompanyUserArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  createCompanyUserInput: CreateCompanyUserInput;
  userId: Scalars['String']['input'];
};


export type MutationCreateContactPersonArgs = {
  createContactPersonInput: CreateContactPersonInput;
};


export type MutationCreateContactPersonTimeslotArgs = {
  createContactPersonTimeslotInput: CreateContactPersonTimeslotInput;
};


export type MutationCreateDeviceTokenArgs = {
  applicantId: Scalars['String']['input'];
  createDeviceTokenInput: CreateDeviceTokenInput;
};


export type MutationCreateFairArgs = {
  createFairInput: CreateFairInput;
  fairDays: Array<CreateFairDayInput>;
};


export type MutationCreateFairDayArgs = {
  createFairDayInput: CreateFairDayInput;
};


export type MutationCreateFairJobArgs = {
  createFairJobInput: CreateFairJobInput;
};


export type MutationCreateFgadminArgs = {
  createFgadminInput: CreateFgadminInput;
  id: Scalars['String']['input'];
};


export type MutationCreateFirebaseBridgeUserArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  firebaseUser: FirebaseCreateUserDto;
  userRights?: InputMaybe<CreateUserRightInput>;
};


export type MutationCreateJobActionArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  createJobActionInput: CreateJobActionInput;
  jobAdvertId: Scalars['String']['input'];
};


export type MutationCreateJobActionHistoryArgs = {
  createJobActionHistoryInput: CreateJobActionHistoryInput;
  jobActionId: Scalars['String']['input'];
};


export type MutationCreateJobAdArgs = {
  categoryIds: Array<Scalars['String']['input']>;
  companyId: Scalars['String']['input'];
  createJobAdvertInput: CreateJobAdvertInput;
  responsibleUsersIds?: InputMaybe<Array<Scalars['String']['input']>>;
};


export type MutationCreateJobAdDynamicLinkArgs = {
  createDynamicLinkInput: CreateJobAdDynamicLinkInput;
};


export type MutationCreateJobCategoryArgs = {
  createJobCategoryInput: CreateJobCategoryInput;
};


export type MutationCreateJobsFilterArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  createJobsFilterInput: CreateJobsFilterInput;
};


export type MutationCreateMessageArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  chatRoomId: Scalars['String']['input'];
  companyFairContactPersonId?: InputMaybe<Scalars['String']['input']>;
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  createMessageInput: CreateMessageInput;
};


export type MutationCreateNotificationArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  createEmailNotificationInput?: InputMaybe<CreateEmailNotificationInput>;
  createNotificationInput: CreateNotificationInput;
  createPushNotificationInput?: InputMaybe<CreatePushNotificationInput>;
};


export type MutationCreatePartnerLinkArgs = {
  createPartnerLinkInput: CreatePartnerLinkInput;
};


export type MutationCreatePaymentIntentArgs = {
  createPaymentIntentInput: CreatePaymentIntentInput;
};


export type MutationCreatePortalSessionArgs = {
  createCheckoutSessionInput: CreatePortalSessionInput;
};


export type MutationCreateSetupCheckoutDetailsArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  customer?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateStaleImageArgs = {
  createStaleImageInput: CreateStaleImageInput;
};


export type MutationCreateStripeCustomerArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  createStripeCustomerInput: StripeCustomerInput;
};


export type MutationCreateStripeSubscriptionArgs = {
  createStripeSubscriptionInput: CreateStripeSubscriptionInput;
  subType?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateSubscriptionArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  createSubscriptionInput: CreateSubscriptionInput;
  jobAdvertId: Scalars['String']['input'];
};


export type MutationCreateSuperUserArgs = {
  createSuperUserInput: CreateSuperUserInput;
  userId: Scalars['String']['input'];
};


export type MutationCreateTimeslotArgs = {
  createTimeslotInput: CreateTimeslotInput;
};


export type MutationCreateUserRightsArgs = {
  companyId: Scalars['String']['input'];
  companyUserId: Scalars['String']['input'];
  createUserRightInput: CreateUserRightInput;
};


export type MutationDeclineApplicantArgs = {
  declineReason?: InputMaybe<Scalars['String']['input']>;
  jobActionId: Scalars['String']['input'];
};


export type MutationDeclineJobActionArgs = {
  jobActionId: Scalars['String']['input'];
};


export type MutationDeleteImageArgs = {
  imageUrl: Scalars['String']['input'];
};


export type MutationDeleteNotificationArgs = {
  notificationId: Scalars['String']['input'];
};


export type MutationGenerateSubscriptionInvoiceArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  createStripeSubscriptionInput: CreateStripeSubscriptionInput;
  customerDetails?: InputMaybe<UpdateStripeCustomerInput>;
};


export type MutationLoginArgs = {
  loginInput: LoginRequestDto;
};


export type MutationManageFairArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  isFairManaged: Scalars['Boolean']['input'];
};


export type MutationMarkAllAsReadArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  companyUserId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationMarkAsReadArgs = {
  notificationId: Scalars['String']['input'];
};


export type MutationPauseJobAdvertArgs = {
  id: Scalars['String']['input'];
};


export type MutationRegisterArgs = {
  registerInput?: InputMaybe<RegisterDto>;
};


export type MutationRemoveApplicantArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRemoveApplicantDocumentArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveAppointmentArgs = {
  id: Scalars['String']['input'];
  rejectReason?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRemoveChatRoomArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveCompanyArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveCompanyFairContactPersonArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveCompanyFairJobArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveCompanyFairParticipationArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveCompanyFromFairArgs = {
  companyId: Scalars['String']['input'];
  fairId: Scalars['String']['input'];
};


export type MutationRemoveCompanyUserArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveContactPersonArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveContactPersonTimeslotArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveDeviceTokenArgs = {
  token?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRemoveDeviceTokensByApplicantIdArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRemoveFairArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveFairDayArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveFairJobArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveFgadminArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveJobAdvertArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveJobCategoryArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveJobsFilterArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveMultipleStaleImagesArgs = {
  ids: Array<Scalars['String']['input']>;
};


export type MutationRemoveMultipleTimeslotsArgs = {
  ids: Array<Scalars['String']['input']>;
};


export type MutationRemovePartnerLinkArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveTimeslotArgs = {
  id: Scalars['String']['input'];
};


export type MutationResetPasswordArgs = {
  email: Scalars['String']['input'];
};


export type MutationResumeJobAdvertArgs = {
  id: Scalars['String']['input'];
};


export type MutationResumeSubscriptionArgs = {
  subscriptionIdInput: SubscriptionIdInput;
};


export type MutationRetrieveStripeCouponArgs = {
  promoCode: Scalars['String']['input'];
};


export type MutationRetrieveStripePromoCodeArgs = {
  promoCode: Scalars['String']['input'];
};


export type MutationRetrieveSubscriptionInvoiceArgs = {
  invoiceIdInput: InvoiceIdInput;
};


export type MutationSendEmailNotificationArgs = {
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  createEmailNotificationInput?: InputMaybe<CreateEmailNotificationInput>;
  createNotificationInput: CreateNotificationInput;
};


export type MutationSendPushNotificationArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  createEmailNotificationInput?: InputMaybe<CreateEmailNotificationInput>;
  createNotificationInput: CreateNotificationInput;
  createPushNotificationInput?: InputMaybe<CreatePushNotificationInput>;
};


export type MutationSendSubscriptionInvoiceArgs = {
  invoiceIdInput: InvoiceIdInput;
};


export type MutationSetApplicantArgs = {
  createApplicantInput?: InputMaybe<CreateApplicantInput>;
};


export type MutationSetAppointmentAsOldArgs = {
  appointmentId: Scalars['String']['input'];
};


export type MutationSetDeviceTokenArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  setDeviceTokenInput: CreateDeviceTokenInput;
};


export type MutationSetJobActionArgs = {
  createOrUpdateJobActionInput: CreateUpdateJobActionInput;
};


export type MutationSetJobsFilterArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  createJobsFilterInput: CreateJobsFilterInput;
};


export type MutationSubscribeToPremiumArgs = {
  createStripeSubscriptionInput: CreateStripeSubscriptionInput;
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateApplicantArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  updateApplicantInput: UpdateApplicantInput;
};


export type MutationUpdateApplicantDocumentArgs = {
  updateApplicantDocumentInput: UpdateApplicantDocumentInput;
};


export type MutationUpdateAppointmentArgs = {
  updateAppointmentInput: UpdateAppointmentInput;
};


export type MutationUpdateChatRoomArgs = {
  updateChatRoomInput: UpdateChatRoomInput;
};


export type MutationUpdateCompanyArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  updateCompanyInput: UpdateCompanyInput;
};


export type MutationUpdateCompanyFairContactPersonArgs = {
  updateCompanyFairContactPersonInput: UpdateCompanyFairContactPersonInput;
};


export type MutationUpdateCompanyFairJobArgs = {
  updateCompanyFairJobInput: UpdateCompanyFairJobInput;
};


export type MutationUpdateCompanyFairParticipationArgs = {
  updateCompanyFairParticipationInput: UpdateCompanyFairParticipationInput;
};


export type MutationUpdateCompanyUserArgs = {
  updateCompanyUserInput: UpdateCompanyUserInput;
  userRights?: InputMaybe<CreateUserRightInput>;
};


export type MutationUpdateContactPersonArgs = {
  updateContactPersonInput: UpdateContactPersonInput;
};


export type MutationUpdateContactPersonTimeslotArgs = {
  updateContactPersonTimeslotInput: UpdateContactPersonTimeslotInput;
};


export type MutationUpdateDeviceTokenArgs = {
  updateDeviceTokenInput: UpdateDeviceTokenInput;
};


export type MutationUpdateFairArgs = {
  id: Scalars['String']['input'];
  updateFairInput: UpdateFairInput;
};


export type MutationUpdateFairDayArgs = {
  updateFairDayInput: UpdateFairDayInput;
};


export type MutationUpdateFairJobArgs = {
  updateFairJobInput: UpdateFairJobInput;
};


export type MutationUpdateFgadminArgs = {
  updateFgadminInput: UpdateFgadminInput;
};


export type MutationUpdateJobActionArgs = {
  currentState?: InputMaybe<Scalars['String']['input']>;
  updateJobActionInput: UpdateJobActionInput;
};


export type MutationUpdateJobAdvertArgs = {
  categoryIdsToConnect?: InputMaybe<Array<Scalars['String']['input']>>;
  categoryIdsToDisconnect?: InputMaybe<Array<Scalars['String']['input']>>;
  companyId: Scalars['String']['input'];
  id: Scalars['String']['input'];
  responsibleUsersIdsToConnect?: InputMaybe<Array<Scalars['String']['input']>>;
  responsibleUsersIdsToDisconnect?: InputMaybe<Array<Scalars['String']['input']>>;
  updateJobAdvertInput: UpdateJobAdvertInput;
};


export type MutationUpdateJobCategoryArgs = {
  updateJobCategoryInput: UpdateJobCategoryInput;
};


export type MutationUpdateJobsFilterArgs = {
  updateJobsFilterInput: UpdateJobsFilterInput;
};


export type MutationUpdateMessageArgs = {
  applicantId: Scalars['String']['input'];
  chatRoomId: Scalars['String']['input'];
  companyUserId: Scalars['String']['input'];
  id: Scalars['String']['input'];
  updateMessageInput: UpdateMessageInput;
};


export type MutationUpdatePartnerLinkArgs = {
  updatePartnerLinkInput: UpdatePartnerLinkInput;
};


export type MutationUpdateStripeCustomerArgs = {
  createStripeCustomerInput: StripeCustomerInput;
  customerId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateSubscriptionArgs = {
  updateSubscriptionInput: UpdateSubscriptionInput;
};


export type MutationUpdateTimeslotArgs = {
  updateTimeslotInput: UpdateTimeslotInput;
};


export type MutationUpdateUserRightsArgs = {
  companyId: Scalars['String']['input'];
  companyUserId: Scalars['String']['input'];
  id: Scalars['String']['input'];
  updateUserRightInput: UpdateUserRightInput;
};


export type MutationVerifyCompanyUserArgs = {
  email: Scalars['String']['input'];
};


export type MutationVerifyFgAdminArgs = {
  email: Scalars['String']['input'];
};


export type MutationVerifySuperUserArgs = {
  email: Scalars['String']['input'];
};

export type NewLikeInput = {
  applicantName: Scalars['String']['input'];
  email: Scalars['String']['input'];
  jobAdId: Scalars['String']['input'];
  jobAdTitle: Scalars['String']['input'];
};

export type Notification = {
  __typename?: 'Notification';
  applicant?: Maybe<Applicant>;
  companyUser?: Maybe<CompanyUser>;
  createdAt: Scalars['DateTime']['output'];
  emailNotification?: Maybe<EmailNotification>;
  id: Scalars['String']['output'];
  isNew?: Maybe<Scalars['Boolean']['output']>;
  pushNotification?: Maybe<PushNotification>;
  updatedAt: Scalars['DateTime']['output'];
};

export type PaginatedApplicantsResponse = {
  __typename?: 'PaginatedApplicantsResponse';
  items: Array<Applicant>;
  meta: PaginationMeta;
};

export type PaginatedCompaniesResponse = {
  __typename?: 'PaginatedCompaniesResponse';
  items: Array<Company>;
  meta: PaginationMeta;
};

export type PaginatedJobAdvertsResponse = {
  __typename?: 'PaginatedJobAdvertsResponse';
  items: Array<JobAdvert>;
  meta: PaginationMeta;
};

export type PaginationInput = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type PaginationMeta = {
  __typename?: 'PaginationMeta';
  currentPage: Scalars['Int']['output'];
  itemCount: Scalars['Int']['output'];
  itemsPerPage: Scalars['Int']['output'];
  totalItems: Scalars['Int']['output'];
  totalPages: Scalars['Int']['output'];
};

export type PartnerLink = {
  __typename?: 'PartnerLink';
  companyFairParticipations?: Maybe<Array<CompanyFairParticipation>>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
};

export type PaymentIntentResponseDto = {
  __typename?: 'PaymentIntentResponseDto';
  allowed_source_types?: Maybe<Array<Scalars['String']['output']>>;
  amount: Scalars['Float']['output'];
  amount_capturable?: Maybe<Scalars['Float']['output']>;
  amount_received?: Maybe<Scalars['Float']['output']>;
  application?: Maybe<Scalars['String']['output']>;
  application_fee_amount?: Maybe<Scalars['Float']['output']>;
  canceled_at?: Maybe<Scalars['Float']['output']>;
  cancellation_reason?: Maybe<Scalars['String']['output']>;
  capture_method: Scalars['String']['output'];
  charges: Charges;
  client_secret: Scalars['String']['output'];
  confirmation_method?: Maybe<Scalars['String']['output']>;
  created: Scalars['Float']['output'];
  currency: Scalars['String']['output'];
  customer?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  invoice?: Maybe<Scalars['String']['output']>;
  last_payment_error?: Maybe<Scalars['String']['output']>;
  livemode: Scalars['Boolean']['output'];
  metadata?: Maybe<Scalars['String']['output']>;
  next_action?: Maybe<Scalars['String']['output']>;
  next_source_action?: Maybe<Scalars['String']['output']>;
  object: Scalars['String']['output'];
  on_behalf_of?: Maybe<Scalars['String']['output']>;
  payment_method?: Maybe<Scalars['String']['output']>;
  payment_method_options: PaymentMethodOptions;
  payment_method_types: Array<Scalars['String']['output']>;
  receipt_email?: Maybe<Scalars['String']['output']>;
  review?: Maybe<Scalars['String']['output']>;
  setup_future_usage?: Maybe<Scalars['String']['output']>;
  shipping?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  statement_descriptor?: Maybe<Scalars['String']['output']>;
  statement_descriptor_suffix?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  transfer_data?: Maybe<Scalars['String']['output']>;
  transfer_group?: Maybe<Scalars['String']['output']>;
};

export type PaymentMethodOptions = {
  __typename?: 'PaymentMethodOptions';
  card: PaymentMethodOptionsCard;
};

export type PaymentMethodOptionsCard = {
  __typename?: 'PaymentMethodOptionsCard';
  installments?: Maybe<Scalars['String']['output']>;
  network?: Maybe<Scalars['String']['output']>;
  request_three_d_secure?: Maybe<Scalars['String']['output']>;
};

export type PaymentMethodResponseDto = {
  __typename?: 'PaymentMethodResponseDto';
  data?: Maybe<Array<DataObj>>;
  id?: Maybe<Scalars['String']['output']>;
  object?: Maybe<Scalars['String']['output']>;
};

export type PushNotification = {
  __typename?: 'PushNotification';
  body?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  title?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type Query = {
  __typename?: 'Query';
  about: Scalars['String']['output'];
  allApplicantDocuments: Array<ApplicantDocument>;
  allApplicants: Array<Applicant>;
  allChatMessages: Array<Message>;
  allChatRooms: Array<ChatRoom>;
  allCompanies: Array<Company>;
  allCompaniesWithFairStatus: Array<Company>;
  allCompanyFairParticipation: Array<CompanyFairParticipation>;
  allCompanyUsers: Array<CompanyUser>;
  allContactPersonTimeslots: Array<ContactPersonTimeslot>;
  allContactPersons: Array<ContactPerson>;
  allDeviceToken: Array<DeviceToken>;
  allFairDay: Array<FairDay>;
  allFairJobs: Array<FairJob>;
  allFairs: Array<Fair>;
  allFgadmins: Array<Fgadmin>;
  allJobActions: Array<JobAction>;
  allJobAds: Array<JobAdvert>;
  allJobCategories: Array<JobCategory>;
  allJobsFilter: Array<JobsFilter>;
  allNotifications: Array<Notification>;
  allSubscriptions: Array<StripeSubscription>;
  allTimeslots: Array<Timeslot>;
  applicantDocumentByApplicantId: ApplicantDocument;
  applicantDocumentById: ApplicantDocument;
  applicantNotifications: Array<Notification>;
  chatMessageById: Message;
  chatRoomByCriteria: ChatRoom;
  chatRoomById: ChatRoom;
  chatRoomByMatch: ChatRoom;
  companyById: Company;
  companyFairJob: CompanyFairJob;
  companyUserNotifications: Array<Notification>;
  contactPersonByEmail: ContactPerson;
  contactPersonsByCompanyId: Array<ContactPerson>;
  contactPersonsByFairId: Array<ContactPerson>;
  deviceTokenById: DeviceToken;
  fairStats: FairStats;
  findAllAppointments: Array<Appointment>;
  findAppointment: Appointment;
  findAppointmentWithFilters: AppointmentPaginated;
  findAppointmentsByApplicant: Array<Appointment>;
  findAppointmentsByCompany: Array<Appointment>;
  findAppointmentsByContactPerson: Array<Appointment>;
  findAppointmentsByFair: Array<Appointment>;
  findAvailableSlots: Array<ContactPersonTimeslot>;
  findByContactPersonId: Array<ContactPersonTimeslot>;
  findByFair: Array<ContactPersonTimeslot>;
  findCompanyFairContactPersonsByContactPersonId: Array<CompanyFairContactPerson>;
  findFairByCompanyId: Array<CompanyFairParticipation>;
  findFairContactPersonByCompany: Array<CompanyFairContactPerson>;
  findFairContactPersonByFair: Array<CompanyFairContactPerson>;
  findFairContactPersonByParticipation: Array<CompanyFairContactPerson>;
  findFairsByCompanyId: Array<Fair>;
  findJobAdsByCompanyIdForApplicant: Array<JobAdvert>;
  findJobAdsByCompanyIdForCompany: Array<JobAdvert>;
  findNotification: Notification;
  findParticipationByFairAndCompany: CompanyFairParticipation;
  findParticipationByFairId: Array<CompanyFairParticipation>;
  findTimeslotByDateRange: Array<Timeslot>;
  findTimeslotByFairId: Array<Timeslot>;
  findUsersByCompany: Array<CompanyUser>;
  generateAgoraToken: Agora;
  getAllCompanyFairContactPersons: Array<CompanyFairContactPerson>;
  getApplicant: Applicant;
  getCompanyFairContactPersonById: CompanyFairContactPerson;
  getCompanyFairJobByFairJobId: Array<CompanyFairJob>;
  getCompanyFairJobByParticipationId: Array<CompanyFairJob>;
  getCompanyFairParticipation: CompanyFairParticipation;
  getCompanyUser: CompanyUser;
  getContactPerson: ContactPerson;
  getContactPersonTimeslot: ContactPersonTimeslot;
  getFair: Fair;
  getFairDay: FairDay;
  getFairDayByDate: Array<FairDay>;
  getFairDayByFair: Array<FairDay>;
  getFairJob: FairJob;
  getFairJobByCompanyParticipationId: Array<FairJob>;
  getFairJobByFairId: Array<FairJob>;
  getFairJobByTitle: FairJob;
  getFgadmin: Fgadmin;
  getFilterOptions: FilterOptionsResponse;
  getJobsFilterByApplicantId: JobsFilter;
  getSubscriptionById: StripeSubscription;
  getTimeslot: Timeslot;
  jobActionById: JobAction;
  jobActionHistory: Array<JobActionHistory>;
  jobActionsByApplicantId: Array<JobAction>;
  jobAdById: JobAdvert;
  jobAdByIdForCompany?: Maybe<JobAdvert>;
  jobAdStatsByCompanyId: JobAdvertStats;
  jobAdStatsById: JobAdvertStats;
  jobAdsBookmarked: Array<JobAdvert>;
  jobAdsByCompanyId: Array<JobAdvert>;
  jobAdsByFilter: Array<JobAdvert>;
  jobAdsByLocation: Array<JobAdvert>;
  jobAdsNotBookmarked: Array<JobAdvert>;
  jobAdsSeen: Array<JobAdvert>;
  jobAdsUnSeen: Array<JobAdvert>;
  jobCategoryById: JobCategory;
  jobLikesByAdvertId: Array<JobAction>;
  jobsFilterById: JobsFilter;
  paginatedApplicants: PaginatedApplicantsResponse;
  paginatedCompanies: PaginatedCompaniesResponse;
  paginatedJobAds: PaginatedJobAdvertsResponse;
  partnerLinks: Array<PartnerLink>;
  retrievePaymentMethods: PaymentMethodResponseDto;
  retrieveStripeCustomer: CreateStripeCustomerResponseDto;
  retrieveSubscriptionMetadata: SubscriptionMetadataResponseDto;
  sendNewLikeEmail: Scalars['String']['output'];
  sendRestPasswordEmail: Scalars['String']['output'];
  sendWelcomeEmail: Scalars['String']['output'];
  subscriptionsByCompanyId: Array<StripeSubscription>;
};


export type QueryAllCompaniesWithFairStatusArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryApplicantDocumentByApplicantIdArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryApplicantDocumentByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryApplicantNotificationsArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryChatMessageByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryChatRoomByCriteriaArgs = {
  criteria: ChatRoomCriteriaInput;
};


export type QueryChatRoomByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryChatRoomByMatchArgs = {
  jobActionId: Scalars['String']['input'];
};


export type QueryCompanyByIdArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryCompanyFairJobArgs = {
  id: Scalars['String']['input'];
};


export type QueryCompanyUserNotificationsArgs = {
  companyUserId: Scalars['String']['input'];
};


export type QueryContactPersonByEmailArgs = {
  email: Scalars['String']['input'];
};


export type QueryContactPersonsByCompanyIdArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryContactPersonsByFairIdArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryDeviceTokenByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryFindAppointmentArgs = {
  id: Scalars['String']['input'];
};


export type QueryFindAppointmentWithFiltersArgs = {
  filter?: InputMaybe<AppointmentFilterInput>;
  skip?: InputMaybe<Scalars['Float']['input']>;
  take?: InputMaybe<Scalars['Float']['input']>;
};


export type QueryFindAppointmentsByApplicantArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryFindAppointmentsByCompanyArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryFindAppointmentsByContactPersonArgs = {
  contactPersonId: Scalars['String']['input'];
};


export type QueryFindAppointmentsByFairArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryFindAvailableSlotsArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryFindByContactPersonIdArgs = {
  contactPersonId: Scalars['String']['input'];
};


export type QueryFindByFairArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryFindCompanyFairContactPersonsByContactPersonIdArgs = {
  contactPersonId: Scalars['String']['input'];
};


export type QueryFindFairByCompanyIdArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryFindFairContactPersonByCompanyArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryFindFairContactPersonByFairArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryFindFairContactPersonByParticipationArgs = {
  participationId: Scalars['String']['input'];
};


export type QueryFindFairsByCompanyIdArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryFindJobAdsByCompanyIdForApplicantArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryFindJobAdsByCompanyIdForCompanyArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryFindNotificationArgs = {
  id: Scalars['String']['input'];
};


export type QueryFindParticipationByFairAndCompanyArgs = {
  companyId: Scalars['String']['input'];
  fairId: Scalars['String']['input'];
};


export type QueryFindParticipationByFairIdArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryFindTimeslotByDateRangeArgs = {
  endDate: Scalars['DateTime']['input'];
  startDate: Scalars['DateTime']['input'];
};


export type QueryFindTimeslotByFairIdArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryFindUsersByCompanyArgs = {
  companyId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGenerateAgoraTokenArgs = {
  createAgoraInput: CreateAgoraInput;
};


export type QueryGetApplicantArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetCompanyFairContactPersonByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetCompanyFairJobByFairJobIdArgs = {
  fairJobId: Scalars['String']['input'];
};


export type QueryGetCompanyFairJobByParticipationIdArgs = {
  companyParticipationId: Scalars['String']['input'];
};


export type QueryGetCompanyFairParticipationArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetCompanyUserArgs = {
  companyUserId: Scalars['String']['input'];
};


export type QueryGetContactPersonArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetContactPersonTimeslotArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetFairArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetFairDayArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetFairDayByDateArgs = {
  date: Scalars['DateTime']['input'];
};


export type QueryGetFairDayByFairArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryGetFairJobArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetFairJobByCompanyParticipationIdArgs = {
  companyParticipationId: Scalars['String']['input'];
};


export type QueryGetFairJobByFairIdArgs = {
  fairId: Scalars['String']['input'];
};


export type QueryGetFairJobByTitleArgs = {
  title: Scalars['String']['input'];
};


export type QueryGetFgadminArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetJobsFilterByApplicantIdArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSubscriptionByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetTimeslotArgs = {
  id: Scalars['String']['input'];
};


export type QueryJobActionByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryJobActionsByApplicantIdArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryJobAdByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryJobAdByIdForCompanyArgs = {
  id: Scalars['String']['input'];
};


export type QueryJobAdStatsByCompanyIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryJobAdStatsByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryJobAdsBookmarkedArgs = {
  applicantId: Scalars['String']['input'];
};


export type QueryJobAdsByCompanyIdArgs = {
  companyId: Scalars['String']['input'];
};


export type QueryJobAdsByFilterArgs = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  filterOptions?: InputMaybe<JobAdvertFilterOptionsInput>;
  includeDisliked?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryJobAdsByLocationArgs = {
  latitude?: InputMaybe<Scalars['Float']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  radius?: InputMaybe<Scalars['Float']['input']>;
};


export type QueryJobAdsNotBookmarkedArgs = {
  applicantId: Scalars['String']['input'];
};


export type QueryJobAdsSeenArgs = {
  applicantId: Scalars['String']['input'];
};


export type QueryJobAdsUnSeenArgs = {
  applicantId: Scalars['String']['input'];
};


export type QueryJobCategoryByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryJobLikesByAdvertIdArgs = {
  advertId: Scalars['String']['input'];
};


export type QueryJobsFilterByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryPaginatedApplicantsArgs = {
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryPaginatedCompaniesArgs = {
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryPaginatedJobAdsArgs = {
  paginationInput?: InputMaybe<PaginationInput>;
};


export type QueryRetrievePaymentMethodsArgs = {
  customerInput: CustomerIdInput;
};


export type QueryRetrieveStripeCustomerArgs = {
  customerInput: CustomerIdInput;
};


export type QueryRetrieveSubscriptionMetadataArgs = {
  subscriptionIdInput: SubscriptionIdInput;
};


export type QuerySendNewLikeEmailArgs = {
  data: NewLikeInput;
};


export type QuerySendRestPasswordEmailArgs = {
  data: ResetPasswordInput;
};


export type QuerySendWelcomeEmailArgs = {
  data: WelcomeEmailInput;
};


export type QuerySubscriptionsByCompanyIdArgs = {
  companyId: Scalars['String']['input'];
};

export type RegisterDto = {
  firebaseUid?: InputMaybe<Scalars['String']['input']>;
};

export type RegisterResponse = {
  __typename?: 'RegisterResponse';
  error?: Maybe<ErrorType>;
  user?: Maybe<User>;
};

export type ResetPasswordInput = {
  email: Scalars['String']['input'];
  resetLink: Scalars['String']['input'];
};

export type Sepa = {
  __typename?: 'Sepa';
  bank_code?: Maybe<Scalars['String']['output']>;
  branch_code?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  fingerprint?: Maybe<Scalars['String']['output']>;
  last4?: Maybe<Scalars['String']['output']>;
};

export type StaleImage = {
  __typename?: 'StaleImage';
  id: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type StripeAddressInput = {
  /** City */
  city: Scalars['String']['input'];
  /** Country */
  country: Scalars['String']['input'];
  /** Address line 1 */
  line1: Scalars['String']['input'];
  /** Address line 2 */
  line2?: InputMaybe<Scalars['String']['input']>;
  /** Postal code */
  postal_code: Scalars['String']['input'];
  /** State */
  state?: InputMaybe<Scalars['String']['input']>;
};

export type StripeCouponResponseDto = {
  __typename?: 'StripeCouponResponseDto';
  amount_off?: Maybe<Scalars['Float']['output']>;
  created: Scalars['Float']['output'];
  currency?: Maybe<Scalars['String']['output']>;
  duration: Scalars['String']['output'];
  duration_in_months?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  livemode: Scalars['Boolean']['output'];
  max_redemptions?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  object: Scalars['String']['output'];
  percent_off: Scalars['Float']['output'];
  redeem_by?: Maybe<Scalars['Float']['output']>;
  times_redeemed: Scalars['Float']['output'];
  valid: Scalars['Boolean']['output'];
};

export type StripeCustomerInput = {
  /** Address */
  address?: InputMaybe<StripeAddressInput>;
  description?: InputMaybe<Scalars['String']['input']>;
  /** email */
  email: Scalars['String']['input'];
  /** name */
  name: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  /** Shipping */
  shipping?: InputMaybe<StripeShippingInput>;
};

export type StripeInvoiceResponseDto = {
  __typename?: 'StripeInvoiceResponseDto';
  account_country: Scalars['String']['output'];
  account_name: Scalars['String']['output'];
  amount_due: Scalars['Float']['output'];
  amount_paid: Scalars['Float']['output'];
  amount_remaining: Scalars['Float']['output'];
  amount_shipping: Scalars['Float']['output'];
  application?: Maybe<Scalars['Float']['output']>;
  application_fee_amount?: Maybe<Scalars['Float']['output']>;
  attempt_count: Scalars['Float']['output'];
  attempted: Scalars['Boolean']['output'];
  auto_advance: Scalars['Boolean']['output'];
  billing_reason: Scalars['String']['output'];
  charge?: Maybe<Scalars['Float']['output']>;
  collection_method: Scalars['String']['output'];
  created: Scalars['Float']['output'];
  currency: Scalars['String']['output'];
  customer: Scalars['String']['output'];
  customer_address?: Maybe<Scalars['String']['output']>;
  customer_email: Scalars['String']['output'];
  customer_name: Scalars['String']['output'];
  customer_phone?: Maybe<Scalars['String']['output']>;
  customer_tax_exempt: Scalars['String']['output'];
  customer_tax_ids: Array<Scalars['String']['output']>;
  default_payment_method?: Maybe<Scalars['String']['output']>;
  default_tax_rates: Array<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  discount?: Maybe<Scalars['String']['output']>;
  discounts: Array<Scalars['String']['output']>;
  due_date?: Maybe<Scalars['String']['output']>;
  ending_balance?: Maybe<Scalars['String']['output']>;
  footer?: Maybe<Scalars['String']['output']>;
  from_invoice?: Maybe<Scalars['String']['output']>;
  hosted_invoice_url?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  invoice_pdf?: Maybe<Scalars['String']['output']>;
  issuer: Issuer;
  last_finalization_error?: Maybe<Scalars['String']['output']>;
  latest_revision?: Maybe<Scalars['String']['output']>;
  lines: Lines;
  livemode: Scalars['Boolean']['output'];
  next_payment_attempt?: Maybe<Scalars['String']['output']>;
  number?: Maybe<Scalars['String']['output']>;
  object: Scalars['String']['output'];
  on_behalf_of?: Maybe<Scalars['String']['output']>;
  paid: Scalars['Boolean']['output'];
  paid_out_of_band: Scalars['Boolean']['output'];
  payment_intent: PaymentIntentResponseDto;
  payment_settings?: Maybe<Scalars['String']['output']>;
  period_end: Scalars['Float']['output'];
  period_start: Scalars['Float']['output'];
  post_payment_credit_notes_amount: Scalars['Float']['output'];
  pre_payment_credit_notes_amount: Scalars['Float']['output'];
  quote?: Maybe<Scalars['String']['output']>;
  receipt_number?: Maybe<Scalars['String']['output']>;
  rendering_options?: Maybe<Scalars['String']['output']>;
  shipping_cost?: Maybe<Scalars['String']['output']>;
  shipping_details?: Maybe<Scalars['String']['output']>;
  starting_balance: Scalars['Float']['output'];
  statement_descriptor?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  subscription?: Maybe<Scalars['String']['output']>;
  subtotal: Scalars['Float']['output'];
  subtotal_excluding_tax: Scalars['Float']['output'];
  tax?: Maybe<Scalars['String']['output']>;
  test_clock?: Maybe<Scalars['String']['output']>;
  total: Scalars['Float']['output'];
  total_discount_amounts: Array<Scalars['String']['output']>;
  total_excluding_tax: Scalars['Float']['output'];
  total_tax_amounts: Array<Scalars['String']['output']>;
  transfer_data?: Maybe<Scalars['String']['output']>;
  webhooks_delivered_at: Scalars['Float']['output'];
};

export type StripePromoCodeResponseDto = {
  __typename?: 'StripePromoCodeResponseDto';
  coupon: StripeCouponResponseDto;
  created: Scalars['Float']['output'];
  id: Scalars['String']['output'];
  livemode: Scalars['Boolean']['output'];
  max_redemptions?: Maybe<Scalars['Float']['output']>;
  object: Scalars['String']['output'];
  times_redeemed: Scalars['Float']['output'];
};

export type StripeShippingInput = {
  /** Address */
  address: StripeAddressInput;
  /** Name */
  name?: InputMaybe<Scalars['String']['input']>;
  /** Phone */
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type StripeSubscription = {
  __typename?: 'StripeSubscription';
  amountTotal?: Maybe<Scalars['Float']['output']>;
  cancelAtPeriodEnd?: Maybe<Scalars['Boolean']['output']>;
  checkoutSessionId: Scalars['String']['output'];
  company: Company;
  companyId: Scalars['String']['output'];
  currency: Scalars['String']['output'];
  expiresAt: Scalars['DateTime']['output'];
  id: Scalars['String']['output'];
  invoiceId: Scalars['String']['output'];
  isActive?: Maybe<Scalars['Boolean']['output']>;
  jobAdvert: JobAdvert;
  jobAdvertId: Scalars['String']['output'];
  paymentStatus: Scalars['String']['output'];
  percent_off: Scalars['Float']['output'];
  plan: Scalars['String']['output'];
  status: Scalars['String']['output'];
  stripeCustomerId?: Maybe<Scalars['String']['output']>;
  stripeSubscriptionId: Scalars['String']['output'];
  subscriptionId?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionIdInput = {
  /** Stripe Subscription ID */
  subscriptionId: Scalars['String']['input'];
};

export type SubscriptionMetadataResponseDto = {
  __typename?: 'SubscriptionMetadataResponseDto';
  advertId?: Maybe<Scalars['String']['output']>;
  advertIds?: Maybe<Scalars['String']['output']>;
  advertTitle?: Maybe<Scalars['String']['output']>;
  advertTitles?: Maybe<Scalars['String']['output']>;
  companyId?: Maybe<Scalars['Float']['output']>;
};

export type SuperUser = {
  __typename?: 'SuperUser';
  avatarImageUrl?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
  user: User;
  userId: Scalars['String']['output'];
};

export type TimeRangeInput = {
  endTime?: InputMaybe<Scalars['String']['input']>;
  startTime?: InputMaybe<Scalars['String']['input']>;
};

export type Timeslot = {
  __typename?: 'Timeslot';
  endTime: Scalars['DateTime']['output'];
  fair: Fair;
  fairId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  startTime: Scalars['DateTime']['output'];
};

export type UpdateApplicantDocumentInput = {
  documentPreviewUrl?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  storage?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateApplicantInput = {
  availableFrom?: InputMaybe<Scalars['DateTime']['input']>;
  birthDate?: InputMaybe<Scalars['DateTime']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  environment?: InputMaybe<Scalars['Int']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  graduation?: InputMaybe<Scalars['String']['input']>;
  lastActive?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  personality?: InputMaybe<Scalars['Int']['input']>;
  profileImageUrl?: InputMaybe<Scalars['String']['input']>;
  receiveNotifications?: InputMaybe<Scalars['Boolean']['input']>;
  schoolName?: InputMaybe<Scalars['String']['input']>;
  strengths?: InputMaybe<Array<Scalars['String']['input']>>;
  subjects?: InputMaybe<Array<Scalars['String']['input']>>;
  weaknesses?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type UpdateAppointmentInput = {
  applicantId?: InputMaybe<Scalars['String']['input']>;
  contactPersonTimeslotId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  rejectReason?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<AppointmentStatus>;
};

export type UpdateChatRoomInput = {
  id: Scalars['String']['input'];
  status?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateCompanyFairContactPersonInput = {
  companyFairParticipationId?: InputMaybe<Scalars['String']['input']>;
  contactPersonId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
};

export type UpdateCompanyFairJobInput = {
  companyFairParticipationId?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  fairJobId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
};

export type UpdateCompanyFairParticipationInput = {
  categoryIds?: InputMaybe<Array<Scalars['String']['input']>>;
  companyId?: InputMaybe<Scalars['String']['input']>;
  fairId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  partnerLinkIds?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type UpdateCompanyInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  detailContent?: InputMaybe<Scalars['String']['input']>;
  dynamicLink?: InputMaybe<Scalars['String']['input']>;
  foundingYear?: InputMaybe<Scalars['Float']['input']>;
  headerImageUrl?: InputMaybe<Scalars['String']['input']>;
  isFairManaged?: InputMaybe<Scalars['Boolean']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  logoImageUrl?: InputMaybe<Scalars['String']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  stripeCustomerId?: InputMaybe<Scalars['String']['input']>;
  totalEmployees?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateCompanyUserInput = {
  activeCompanyId?: InputMaybe<Scalars['String']['input']>;
  avatarImageUrl?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateContactPersonInput = {
  companyId?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateContactPersonTimeslotInput = {
  available?: InputMaybe<Scalars['Boolean']['input']>;
  companyFairContactPersonId?: InputMaybe<Scalars['String']['input']>;
  endTime?: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['String']['input'];
  startTime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdateDeviceTokenInput = {
  token?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateFairDayInput = {
  day?: InputMaybe<Scalars['DateTime']['input']>;
  endTime?: InputMaybe<Scalars['DateTime']['input']>;
  fairId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  startTime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdateFairInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  contactPersonEmail?: InputMaybe<Scalars['String']['input']>;
  contactPersonName?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  id: Scalars['String']['input'];
  location?: InputMaybe<Scalars['String']['input']>;
  locationName?: InputMaybe<Scalars['String']['input']>;
  logoImageUrl?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  publisherLogoImageUrl?: InputMaybe<Scalars['String']['input']>;
  publisherName?: InputMaybe<Scalars['String']['input']>;
  registrationEndDate?: InputMaybe<Scalars['DateTime']['input']>;
  registrationStartDate?: InputMaybe<Scalars['DateTime']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateFairJobInput = {
  id: Scalars['String']['input'];
  title?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateFgadminInput = {
  avatarImageUrl?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateJobActionInput = {
  applicantIsNew?: InputMaybe<Scalars['Boolean']['input']>;
  companyIsNew?: InputMaybe<Scalars['Boolean']['input']>;
  declineReason?: InputMaybe<Scalars['String']['input']>;
  deletedFromApplicant?: InputMaybe<Scalars['Boolean']['input']>;
  deletedFromCompany?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['String']['input'];
  state?: InputMaybe<ActionState>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateJobAdvertInput = {
  activeFromDate?: InputMaybe<Scalars['DateTime']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  approved?: InputMaybe<Scalars['Boolean']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  companyName?: InputMaybe<Scalars['String']['input']>;
  companyUserId?: InputMaybe<Scalars['String']['input']>;
  declineReason?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  detailDescription?: InputMaybe<Scalars['String']['input']>;
  district?: InputMaybe<Scalars['String']['input']>;
  educationDuration?: InputMaybe<Scalars['Float']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  gehalt?: InputMaybe<Array<Scalars['Float']['input']>>;
  headerImageUrl?: InputMaybe<Scalars['String']['input']>;
  holidayDays?: InputMaybe<Scalars['Float']['input']>;
  imageUrl?: InputMaybe<Scalars['String']['input']>;
  impressions?: InputMaybe<Scalars['Float']['input']>;
  isDeclined?: InputMaybe<Scalars['Boolean']['input']>;
  isDeleted?: InputMaybe<Scalars['Boolean']['input']>;
  isDraft?: InputMaybe<Scalars['Boolean']['input']>;
  jobCategoryId?: InputMaybe<Scalars['String']['input']>;
  latitude?: InputMaybe<Scalars['Float']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  workHours?: InputMaybe<Scalars['Float']['input']>;
};

export type UpdateJobCategoryInput = {
  id: Scalars['String']['input'];
  imageUrl?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateJobsFilterInput = {
  categories?: InputMaybe<Array<Scalars['String']['input']>>;
  currentLocation?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  latitude?: InputMaybe<Scalars['Float']['input']>;
  longitude?: InputMaybe<Scalars['Float']['input']>;
  radius?: InputMaybe<Scalars['Float']['input']>;
  type?: InputMaybe<Array<JobAdvertType>>;
};

export type UpdateMessageInput = {
  authorName?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  deletedAt?: InputMaybe<Scalars['DateTime']['input']>;
  deletedBy?: InputMaybe<Scalars['String']['input']>;
  deletedById?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  isApplicant?: InputMaybe<Scalars['Boolean']['input']>;
  isCompany?: InputMaybe<Scalars['Boolean']['input']>;
  isDeleted?: InputMaybe<Scalars['Boolean']['input']>;
  isDelivered?: InputMaybe<Scalars['Boolean']['input']>;
  isSeen?: InputMaybe<Scalars['Boolean']['input']>;
  isSent?: InputMaybe<Scalars['Boolean']['input']>;
};

export type UpdatePartnerLinkInput = {
  id: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateStripeCustomerInput = {
  /** Address */
  address?: InputMaybe<StripeAddressInput>;
  description?: InputMaybe<Scalars['String']['input']>;
  /** email */
  email?: InputMaybe<Scalars['String']['input']>;
  /** name */
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  /** Shipping */
  shipping?: InputMaybe<StripeShippingInput>;
};

export type UpdateSubscriptionInput = {
  amountTotal: Scalars['Float']['input'];
  checkoutSessionId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  expiresAt: Scalars['DateTime']['input'];
  /** Subscription ID */
  id: Scalars['String']['input'];
  invoiceId: Scalars['String']['input'];
  isActive?: Scalars['Boolean']['input'];
  paymentStatus: Scalars['String']['input'];
  percent_off?: InputMaybe<Scalars['Float']['input']>;
  plan: Scalars['String']['input'];
  status?: InputMaybe<Scalars['String']['input']>;
  stripeCustomerId: Scalars['String']['input'];
  stripeSubscriptionId: Scalars['String']['input'];
};

export type UpdateTimeslotInput = {
  endTime?: InputMaybe<Scalars['DateTime']['input']>;
  fairId?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  startTime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdateUserRightInput = {
  companyAdmin?: InputMaybe<Scalars['Boolean']['input']>;
  createJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  createUser?: InputMaybe<Scalars['Boolean']['input']>;
  deleteJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  editCompany?: InputMaybe<Scalars['Boolean']['input']>;
  editJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  id: Scalars['String']['input'];
  superAdmin?: InputMaybe<Scalars['Boolean']['input']>;
  updateJobAd?: InputMaybe<Scalars['Boolean']['input']>;
  viewApplicants?: InputMaybe<Scalars['Boolean']['input']>;
  viewJobAd?: InputMaybe<Scalars['Boolean']['input']>;
};

export type User = {
  __typename?: 'User';
  firebaseUid: Scalars['String']['output'];
  id: Scalars['String']['output'];
};

export type UserRight = {
  __typename?: 'UserRight';
  company: Company;
  companyAdmin?: Maybe<Scalars['Boolean']['output']>;
  companyId: Scalars['String']['output'];
  companyUser: CompanyUser;
  createJobAd?: Maybe<Scalars['Boolean']['output']>;
  createUser?: Maybe<Scalars['Boolean']['output']>;
  deleteJobAd?: Maybe<Scalars['Boolean']['output']>;
  editCompany?: Maybe<Scalars['Boolean']['output']>;
  editJobAd?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  superAdmin?: Maybe<Scalars['Boolean']['output']>;
  updateJobAd?: Maybe<Scalars['Boolean']['output']>;
  viewApplicants?: Maybe<Scalars['Boolean']['output']>;
  viewJobAd?: Maybe<Scalars['Boolean']['output']>;
};

export type WelcomeEmailInput = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  verificationLink?: InputMaybe<Scalars['String']['input']>;
};
