import {
  ApolloClient,
  InMemoryCache,
  createHttpLink,
  from,
} from '@apollo/client/core'
import { onError } from '@apollo/client/link/error'
import { setContext } from '@apollo/client/link/context'
import { GraphQLWsLink } from '@apollo/client/link/subscriptions'
import { getMainDefinition } from '@apollo/client/utilities'
import { createClient } from 'graphql-ws'
import { DebugLink } from '@storipress/apollo-vue-devtool'
import { getIdToken } from '@/api/middleware/getToken'
import { DEV_API_URL, DEV_WS_URL } from '@/constants/app'
import { split } from '@apollo/client/core'
import { useAuthStore } from '@/stores/authStore'

export function createApolloClient() {
  const authLink = setContext(async (request, { headers }) => {
    if (request.operationName !== 'passwordReset') {
      const token = await getIdToken()
      return {
        headers: {
          ...headers,
          authorization: token ? `Bearer ${token}` : '',
        },
      }
    } else {
      return {
        headers: {
          ...headers,
        },
      }
    }
  })

  const errorLink = onError(({ graphQLErrors, networkError }) => {
    if (graphQLErrors) {
      // Check for authentication errors
      const authError = graphQLErrors.find(
        error => 
          error.message.includes('token expired') || 
          error.message.includes('not authenticated') ||
          error.message.includes('unauthorized') ||
          error.extensions?.code === 'UNAUTHENTICATED'
      )

      if (authError) {
        console.log('Authentication error detected:', authError)
        // Get auth store and sign out user
        const authStore = useAuthStore()
        authStore.signOut()
        return
      }

      // log and handle other graphql errors - send to sentry
      console.log({ gqlErrors: graphQLErrors })
    }
    if (networkError) {
      // Check for network errors that might indicate auth problems
      const networkErrorStr = networkError.toString().toLowerCase()
      if (
        networkErrorStr.includes('unauthorized') || 
        networkErrorStr.includes('forbidden') ||
        networkErrorStr.includes('401') ||
        networkErrorStr.includes('403')
      ) {
        console.log('Network authentication error detected:', networkError)
        // Get auth store and sign out user
        const authStore = useAuthStore()
        authStore.signOut()
        return
      }

      // log and handle other network errors - send to sentry
      console.log({ networkError })
    }
  })

  const wsLink = () => {
    // const wsUrl = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_WS_URL : DEV_WS_URL
    return new GraphQLWsLink(
      createClient({
        url: DEV_WS_URL,
        connectionParams: async () => {
          const token = await getIdToken()
          return {
            token: token,
          }
        },
      }),
    )
  }

  const httpLink = createHttpLink({
    uri: DEV_API_URL,
  })

  const debugLink = new DebugLink()

  const HttpAuthlink = from([errorLink, authLink, debugLink, httpLink])

  // using the ability to split links, you can send data to each link
  // depending on what kind of operation is being sent
  const link = split(
    // split based on operation type
    ({ query }) => {
      const definition = getMainDefinition(query)
      return (
        definition.kind === 'OperationDefinition' &&
        definition.operation === 'subscription'
      )
    },
    wsLink(),
    HttpAuthlink,
  )

  return new ApolloClient({
    link,
    connectToDevTools: true,
    cache: new InMemoryCache(),
  })
}

export const apolloClient = createApolloClient()
