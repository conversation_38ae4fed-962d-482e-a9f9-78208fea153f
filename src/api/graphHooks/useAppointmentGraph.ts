import { useMutation, useLazyQuery, useQuery } from '@vue/apollo-composable'
import { provideApolloClient } from '@vue/apollo-composable'
import { apolloClient } from '@/api/middleware/apolloClient'
import { useRoute } from 'vue-router'
import {
  FIND_APPOINTMENTS_WITH_FILTERS,
  GET_ALL_APPOINTMENTS,
  GET_APPOINTMENT_BY_ID,
  GET_FILTER_OPTIONS,
} from '@/api/graphql/queries/appointmentQueries'
import {
  DELETE_APPOINTMENT,
  UPDATE_APPOINTMENT_STATUS,
} from '@/api/graphql/mutations/appointmentMutation'
import { AppointmentFilterOptions } from '@/api/apiTypes'
import { useFilterStore } from '@/stores/fairs/filterStore'
import { useAuthStore } from '@/stores/authStore'

export const useAppointmentGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()
  const filterStore = useFilterStore()
  const authStore = useAuthStore()

  const initialFetchDone = ref(true)

  const companyId = computed(() => authStore.companyId)

  const isInFairAppointmentsRoute = computed(() =>
    route.path.includes('/fair/appointments'),
  )

  const isInCompanyAppointmentsRoute = computed(() =>
    route.path.includes('/company/appointments'),
  )

  const {
    result: getAllAppointmentsResult,
    loading: loadingAllAppointments,
    refetch: refetchAppointments,
    load: loadAllAppointments,
  } = useLazyQuery(GET_ALL_APPOINTMENTS)

  const appointmentsList = computed(
    () => getAllAppointmentsResult.value?.findAllAppointments || [],
  )

  const getAppointmentById = (id: string) => {
    const { result, loading, error, load } = useLazyQuery(
      GET_APPOINTMENT_BY_ID,
      { id },
      { fetchPolicy: 'network-only' },
    )

    return {
      appointment: computed(() => result.value?.getAppointment),
      loading,
      error,
      load,
    }
  }

  const {
    result: filteredAppointmentsResult,
    loading: loadingFilteredAppointments,
    error: filteredAppointmentsError,
    onResult: onAppointmentsResult,
    refetch: refetchFilteredAppointments,
  } = useQuery(
    FIND_APPOINTMENTS_WITH_FILTERS,
    {
      filter: {},
      skip: 0,
      take: 10,
    },
    {
      enabled: isInFairAppointmentsRoute,
    },
  )

  const {
    result: filteredCompanyAppointmentsResult,
    loading: loadingFilteredCompanyAppointments,
    error: filteredCompanyAppointmentsError,
    onResult: onCompanyAppointmentsResult,
    refetch: refetchCompanyFilteredAppointments,
  } = useQuery(
    FIND_APPOINTMENTS_WITH_FILTERS,
    {
      filter: { companyId: companyId.value },
      skip: 0,
      take: 10,
    },
    {
      enabled: isInCompanyAppointmentsRoute,
    },
  )

  const filteredAppointments = computed(() => {
    return filteredAppointmentsResult.value?.findAppointmentsWithFilters.items
  })

  onAppointmentsResult(async (res: any) => {
    if (!res.data?.findAppointmentWithFilters.items) {
      return []
    }
    const appointmentCount = res.data.findAppointmentWithFilters.count
    const appointments = computed(
      () => res.data.findAppointmentWithFilters.items || [],
    )
    filterStore.setFilteredAppointments(appointments.value, appointmentCount)
    initialFetchDone.value = false
  })

  onCompanyAppointmentsResult(async (res: any) => {
    if (!res.data?.findAppointmentWithFilters.items) {
      return []
    }
    const appointmentCount = res.data.findAppointmentWithFilters.count
    const appointments = computed(
      () => res.data.findAppointmentWithFilters.items || [],
    )
    filterStore.setFilteredAppointments(appointments.value, appointmentCount)
    initialFetchDone.value = false
  })

  const {
    result: filterOptionsResult,
    loading: loadingFilterOptions,
    refetch: refetchFilterOptions,
    load: loadFilterOptions,
  } = useLazyQuery(GET_FILTER_OPTIONS)

  const filterOptions = computed<AppointmentFilterOptions>(() => {
    if (filterOptionsResult.value?.getFilterOptions) {
      return filterOptionsResult.value.getFilterOptions
    }
    return {
      applicants: [],
      companies: [],
      contactPersons: [],
      fairs: [],
    }
  })

  const { mutate: updateAppointmentStatus, loading: loadingStatusUpdate } =
    useMutation(UPDATE_APPOINTMENT_STATUS)

  const { mutate: deleteAppointment, loading: loadingDelete } =
    useMutation(DELETE_APPOINTMENT)

  const loadFilteredAppointments = () => {
    refetchFilteredAppointments({
      filter: {},
      skip: 0,
      take: 10,
    })
  }

  return {
    state: {
      appointmentsList,
      loadingAllAppointments,
      loadingStatusUpdate,
      loadingDelete,
      filterOptions,
      loadingFilteredAppointments,
      filteredAppointmentsError,
      loadingFilterOptions,
      filteredAppointments,
      filteredCompanyAppointmentsResult,
      loadingFilteredCompanyAppointments,
      filteredCompanyAppointmentsError,
    },
    actions: {
      updateAppointmentStatus,
      deleteAppointment,
      getAppointmentById,
      refetchAppointments,
      loadAllAppointments,
      refetchFilterOptions,
      loadFilterOptions,
      refetchFilteredAppointments,
      refetchCompanyFilteredAppointments,
      loadFilteredAppointments,
    },
  }
}
