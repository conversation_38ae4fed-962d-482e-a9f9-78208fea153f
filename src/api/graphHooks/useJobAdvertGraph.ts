import { apolloClient } from '@/api/middleware/apolloClient'
import {
  useQuery,
  useMutation,
  provideApolloClient,
  useLazyQuery,
} from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import {
  GET_ALL_JOB_ADVERTS,
  GET_JOB_AD_STATISTICS,
  GET_JOB_AD_STATISTICS_BY_COMPANY,
  GET_JOB_ADVERT_BY_ID,
  GET_JOB_ADVERTS_BY_COMPANY_ID,
  GET_JOB_LIKES_BY_JOB_ADVERT_ID,
  GET_PAGINATED_JOB_ADVERTS,
} from '@/api/graphql/queries/jobAdQueries'
import {
  APPROVE_JOB_AD,
  BLOCK_JOB_AD,
  CREATE_JOB_ADVERT,
  EDIT_JOB_ADVERT,
  PAUSE_JOB_AD,
  REMOVE_JOB_AD,
  RESUME_JOB_AD,
} from '@/api/graphql/mutations/jobAdMutations'
import { CreateJobAdvertInput } from '@/gql/graphql'
import { useAuthStore } from '@/stores/authStore'

interface useJobAdvertOptions {
  companyId?: string
  jobAdvertId?: string
}

interface jobAdvertOptions {
  id?: string
  companyId?: string
  categoryId?: string
  editJobAdvertInput?: CreateJobAdvertInput
  createJobAdvertInput?: CreateJobAdvertInput
}

export const useJobAdvertGraph = ({
  companyId = undefined,
  jobAdvertId = undefined,
}: useJobAdvertOptions = {}) => {
  provideApolloClient(apolloClient)

  const authStore = useAuthStore()

  const isSuperUser = computed(() => authStore.claims?.isSuperUser || false)

  const isFairAdmin = computed(() => authStore.claims.isFGAdmin)

  const route = useRoute()
  const cId =
    (route && route.params && route.params.companyId) ||
    companyId ||
    authStore.companyId ||
    (route && route.params && route.params.cid)
  const jId = (route && route.params && route.params.id) || jobAdvertId

  const companyVars = ref({
    companyId: authStore?.companyId,
  })

  const {
    result: getJobAdsByCompany,
    loading: loadingJobAdsByCompany,
    refetch: refetchJobAds,
    load: loadJobAdsByCompany,
  } = useLazyQuery(
    GET_JOB_ADVERTS_BY_COMPANY_ID,
    {
      companyId: cId,
    },
    {
      prefetch: false,
      fetchPolicy: 'cache-and-network',
    },
  )

  const jobAdsByCompany = computed(
    () => getJobAdsByCompany.value?.findJobAdsByCompanyIdForCompany ?? [],
  )

  const {
    result: getAllJobAds,
    loading: loadingAllJobAds,
    refetch: refetchAllJobAds,
    load: loadAllJobAds,
  } = useLazyQuery(GET_ALL_JOB_ADVERTS)

  const allJobAds = computed(() => getAllJobAds.value?.allJobAds ?? [])

  const paginationParams = ref({
    page: 1,
    limit: 10,
    search: '',
    status: 'PENDING',
  })

  const {
    result: paginatedJobAdsResult,
    loading: loadingPaginatedJobAds,
    load: loadPaginatedJobAds,
    refetch: refetchPaginatedJobAds,
  } = useLazyQuery(
    GET_PAGINATED_JOB_ADVERTS,
    () => ({
      paginationInput: paginationParams.value,
    }),
    {
      fetchPolicy: 'network-only',
    },
  )

  const paginatedJobAds = computed(() => {
    return (
      paginatedJobAdsResult.value?.paginatedJobAds || {
        items: [],
        meta: {
          totalItems: 0,
          currentPage: 1,
          totalPages: 1,
          itemsPerPage: 10,
          itemCount: 0,
        },
      }
    )
  })

  const updatePaginationParams = async (
    page: number,
    limit: number,
    search: string = '',
    status: string = '',
  ) => {
    paginationParams.value = { page, limit, search, status }
    await refetchPaginatedJobAds()
  }

  const {
    result: jobAdLikes,
    loading: loadingJobAdvertLikes,
    refetch: doRefetchLikes,
  } = useQuery(
    GET_JOB_LIKES_BY_JOB_ADVERT_ID,
    {
      jobAdvertId: jId,
    },
    {
      enabled: !!jId,
    },
  )

  const jobAdvertLikes = computed(() => {
    return (
      jobAdLikes.value?.jobLikesByAdvertId?.map(
        (like: {
          applicant: {
            firstName: string
            lastName: string
            birthDate: string
            graduation: string
            city: string
            id: string
            profileImageUrl: string
          }
        }) => {
          return {
            ...like,
            applicantId: like.applicant?.id,
            name: like.applicant.firstName + ' ' + like.applicant.lastName,
            birthday: like.applicant.birthDate,
            graduation: like.applicant.graduation,
            city: like.applicant.city,
            image: like.applicant.profileImageUrl,
          }
        },
      ) ?? []
    )
  })

  const refetchLikes = async () => {
    await doRefetchLikes()
  }

  //GET JOB ADVERT BY ID
  // const {
  //   result: singleJobAdvert,
  //   loading: loadingJobAdvert,
  //   refetch: refetchSingleJobAd,
  // } = useQuery(
  //   GET_JOB_ADVERT_BY_ID,
  //   {
  //     jobAdvertId: jId || '',
  //   },
  //   {
  //     enabled: !!jId,
  //     fetchPolicy: 'cache-and-network',
  //     nextFetchPolicy: 'cache-first',
  //   },
  // )
  //
  // const jobAdvert = computed(() => {
  //   return singleJobAdvert.value?.jobAdByIdForCompany || {}
  // })

  //GET JOB ADS BY COMPANY
  const {
    result: jobAdStatsByCompanyId,
    refetch: refetchJobAdStatsByCompany,
    loading: loadingJobAdStatsByCompany,
    error: jobAdStatsByCompanyIdError,
    load: loadJobAdStatsByCompany,
  } = useLazyQuery(
    GET_JOB_AD_STATISTICS_BY_COMPANY,
    { companyId: cId },
    {
      prefetch: false,
    },
  )

  const companyJobAdStats = computed(() => {
    if (loadingJobAdStatsByCompany.value) {
      return { bookmarks: 0, impressions: 0, likes: 0, matches: 0 }
    }
    if (jobAdStatsByCompanyIdError.value) {
      throw jobAdStatsByCompanyIdError
    }

    return {
      ...jobAdStatsByCompanyId.value?.jobAdStatsByCompanyId,
    }
  })

  //GET JOB AD STATISTICS
  const {
    result: getJobAdStats,
    loading: loadingJobAdStats,
    refetch: refetchJobAdStats,
    error: jobAdStatsError,
    load: loadJobAdStats,
  } = useLazyQuery(
    GET_JOB_AD_STATISTICS,
    { advertId: jId },
    { prefetch: false },
  )

  const jobAdStats = computed(() => {
    if (loadingJobAdStats.value) {
      return { bookmarks: 0, impressions: 0, likes: 0, matches: 0 }
    }
    if (jobAdStatsError.value) {
      throw jobAdStatsByCompanyIdError
    }

    return {
      ...getJobAdStats.value?.jobAdStatsById,
    }
  })

  //CREATE JOB ADVERT
  const { mutate: createJobAdvert } = useMutation(CREATE_JOB_ADVERT, {
    refetchQueries: [GET_PAGINATED_JOB_ADVERTS, GET_JOB_ADVERTS_BY_COMPANY_ID],
  })

  //EDIT JOB ADVERT
  const { mutate: updateJobAdvert } = useMutation(EDIT_JOB_ADVERT)

  //Delete JOB ADVERT
  const { mutate: deleteJobAdvert } = useMutation(REMOVE_JOB_AD, {
    update: (cache, { data }) => {
      // Get the ID of the deleted jobAd
      const deletedJobAdId = data?.removeJobAdvert?.id

      // Read the current list of jobAds from the cache
      const deleted = cache.readQuery({
        query: GET_JOB_ADVERTS_BY_COMPANY_ID,
      })
    },
  })
  const saveJobAdvert = async (variables: jobAdvertOptions) => {
    const saveAd = await createJobAdvert(variables)
    if (!saveAd?.errors) {
      await refetchJobAds()
      return saveAd?.data?.createJobAd
    } else {
      throw saveAd?.errors
    }
  }
  const editJobAdvert = async (variables: jobAdvertOptions) => {
    const editJobAd = await updateJobAdvert(variables)
    if (!editJobAd?.errors) {
      await refetchJobAds()
      return editJobAd?.data?.createJobAd
    } else {
      throw editJobAd?.errors
    }
  }

  const { mutate: approveJobAd } = useMutation(APPROVE_JOB_AD, {
    update: (cache, { data }) => {
      if (data?.pauseJobAdvert?.paused) {
        cache.modify({
          id: cache.identify({
            __typename: 'JobAdvert',
            id: data?.pauseJobAdvert?.id,
          }),
          fields: {
            status() {
              return data?.pauseJobAdvert?.status
            },
          },
        })
      }
    },
  })

  const { mutate: pauseJobAd } = useMutation(PAUSE_JOB_AD, {
    update: (cache, { data }) => {
      if (data?.pauseJobAdvert?.paused) {
        cache.modify({
          id: cache.identify({
            __typename: 'JobAdvert',
            id: data?.pauseJobAdvert?.id,
          }),
          fields: {
            status() {
              return data?.pauseJobAdvert?.status
            },
          },
        })
      }
    },
  })

  const { mutate: resumeJobAd } = useMutation(RESUME_JOB_AD, {
    update: (cache, { data }) => {
      if (!data?.pauseJobAdvert?.paused) {
        cache.modify({
          id: cache.identify({
            __typename: 'JobAdvert',
            id: data?.pauseJobAdvert?.id,
          }),
          fields: {
            status() {
              return !data?.pauseJobAdvert?.status
            },
          },
        })
      }
    },
  })

  const { mutate: blockJobAd } = useMutation(BLOCK_JOB_AD, {
    refetchQueries: [GET_JOB_ADVERTS_BY_COMPANY_ID, GET_JOB_ADVERT_BY_ID],
  })

  const refreshApolloCache = async () => {
    try {
      await apolloClient.resetStore()
    } catch (e) {
      console.error('Failed to reset Apollo client cache', e)
    }
  }

  return {
    state: {
      jobAdStats,
      companyJobAdStats,
      loadingJobAdStatsByCompany,
      jobAdvertLikes,
      jobAdLikes,
      jobAdsByCompany,
      allJobAds,
      paginatedJobAds,
      loadingJobAdsByCompany,
      loadingAllJobAds,
      loadingJobAdvertLikes,
      loadingJobAdStats,
      loadingPaginatedJobAds,
    },
    actions: {
      saveJobAdvert,
      editJobAdvert,
      refetchLikes,
      refetchJobAds,
      refetchAllJobAds,
      refetchJobAdStats,
      deleteJobAdvert,
      createJobAdvert,
      approveJobAd,
      blockJobAd,
      pauseJobAd,
      resumeJobAd,
      refreshApolloCache,
      refetchJobAdStatsByCompany,
      loadAllJobAds,
      loadPaginatedJobAds,
      updatePaginationParams,
      refetchPaginatedJobAds,
      loadJobAdsByCompany,
      loadJobAdStatsByCompany,
      loadJobAdStats,
    },
  }
}
