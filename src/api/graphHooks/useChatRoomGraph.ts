import { GET_MATCH_CHAT_ROOM } from '@/api/graphql/queries/chatRoomQueries'
import { apolloClient } from '@/api/middleware/apolloClient'
import { useJobAdsStore } from '@/stores/jobAdsStore'
import { provideApolloClient, useQuery } from '@vue/apollo-composable'
import { useRoute } from 'vue-router'
import { useFilterStore } from '@/stores/fairs/filterStore'

export const useChatRoomGraph = () => {
  provideApolloClient(apolloClient)

  const route = useRoute()
  const applicantId = (route?.params?.uid ?? '') as string
  const jobAdvertId = route?.params?.id as string
  const filterStore = useFilterStore()

  const appointmentId = computed(() => {
    return filterStore.activeAppointment?.id || undefined
  })

  const jobAdsStore = useJobAdsStore()

  const jobActionStateValue = computed(() => {
    return jobAdsStore.jobAdLikes[jobAdvertId]?.find(
      jobAction => jobAction.applicantId === applicantId,
    )?.state
  })

  const jobActionIdValue = computed(() => {
    return (
      jobAdsStore.jobAdLikes[jobAdvertId]?.find(
        jobAction => jobAction.applicantId === applicantId,
      )?.id || undefined
    )
  })

  const {
    result: matchChatRoom,
    refetch: loadMatchChatRoom,
    error: matchChatRoomError,
    loading: loadingMatchChatRoom,
  } = useQuery(
    GET_MATCH_CHAT_ROOM,
    computed(() => ({
      criteria: {
        jobActionId: jobActionIdValue.value,
        appointmentId: appointmentId.value,
      },
    })),
  )

  const chatRoomDetails = computed(() => {
    if (matchChatRoomError.value) {
      console.error(matchChatRoomError.value.message)
      loadingMatchChatRoom.value = false
      return null
    }
    if (loadingMatchChatRoom.value) {
      return {}
    }
    return matchChatRoom.value?.chatRoomByCriteria ?? {}
  })

  const fetchChatRoom = async () => {
    if (jobActionStateValue.value === 'MATCHED' || appointmentId.value) {
      await loadMatchChatRoom()
    }
  }

  return {
    state: {
      chatRoomDetails,
      loadingMatchChatRoom,
    },
    actions: {
      fetchChatRoom,
    },
  }
}
