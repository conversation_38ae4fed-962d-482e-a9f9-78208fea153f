import gql from 'graphql-tag'

export const CREATE_CONTACT_PERSON_TIMESLOT = gql`
  mutation CreateContactPersonTimeslot(
    $input: CreateContactPersonTimeslotInput!
  ) {
    createContactPersonTimeslot(createContactPersonTimeslotInput: $input) {
      id
      companyFairContactPersonId
      startTime
      endTime
      available
    }
  }
`

export const UPDATE_CONTACT_PERSON_TIMESLOT = gql`
  mutation UpdateContactPersonTimeslot(
    $input: UpdateContactPersonTimeslotInput!
  ) {
    updateContactPersonTimeslot(updateContactPersonTimeslotInput: $input) {
      id
      companyFairContactPersonId
      startTime
      endTime
      available
    }
  }
`

export const REMOVE_CONTACT_PERSON_TIMESLOT = gql`
  mutation RemoveContactPersonTimeslot($id: String!) {
    removeContactPersonTimeslot(id: $id) {
      id
    }
  }
`
