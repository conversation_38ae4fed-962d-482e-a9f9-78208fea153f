import gql from 'graphql-tag'

export const CREATE_CHAT_MESSAGE = gql`
  mutation CreateChatMessage(
    $applicantId: String
    $chatRoomId: String!
    $companyUserId: String
    $companyFairContactPersonId: String
    $createMessageInput: CreateMessageInput!
  ) {
    createMessage(
      applicantId: $applicantId
      chatRoomId: $chatRoomId
      companyUserId: $companyUserId
      companyFairContactPersonId: $companyFairContactPersonId
      createMessageInput: $createMessageInput
    ) {
      id
      content
      isCompany
      isApplicant
      isSent
      authorName
      isDelivered
      isSeen
      deletedAt
      deletedBy
      deletedById
      isDeleted
    }
  }
`
