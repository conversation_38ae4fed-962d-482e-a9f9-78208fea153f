import gql from 'graphql-tag'

export const CREATE_COMPANY = gql`
  mutation CreateCompany($companyInput: CreateCompanyInput!) {
    createCompany(companyInput: $companyInput) {
      id
      name
      city
      country
      address
      detailContent
      dynamicLink
      logoImageUrl
      createdAt
      updatedAt
    }
  }
`
export const CREATE_COMPANY_BY_ADMIN = gql`
  mutation CreateCompanyByAdmin($companyInput: CreateCompanyInput!) {
    createCompanyByAdmin(companyInput: $companyInput) {
      id
      name
      city
      country
      address
      detailContent
      dynamicLink
      logoImageUrl
      createdAt
      updatedAt
    }
  }
`
export const UPDATE_COMPANY = gql`
  mutation UpdateCompany($id: String, $companyInput: UpdateCompanyInput!) {
    updateCompany(id: $id, updateCompanyInput: $companyInput) {
      id
      name
      city
      country
      address
      dynamicLink
      stripeCustomerId
      detailContent
      logoImageUrl
      headerImageUrl
      createdAt
      updatedAt
    }
  }
`

export const MANAGE_COMPANY_FAIR = gql`
  mutation ManageCompany($companyId: String!, $isFairManaged: Boolean!) {
    manageFair(id: $companyId, isFairManaged: $isFairManaged) {
      id
      isFairManaged
    }
  }
`

export const DELETE_COMPANY = gql`
  mutation DeleteCompany($id: String!) {
    removeCompany(id: $id) {
      id
      name
    }
  }
`
