import gql from 'graphql-tag'

export const GET_COMPANY_BY_ID = gql`
  query GetCompanyById($companyId: String) {
    companyById(id: $companyId) {
      name
      id
      address
      city
      country
      detailContent
      isFairManaged
      foundingYear
      headerImageUrl
      stripeCustomerId
      logoImageUrl
      latitude
      longitude
      totalEmployees
      dynamicLink
      createdAt
      updatedAt
    }
  }
`
export const GET_All_COMPANIES = gql`
  query GetAllCompanies {
    allCompanies {
      name
      id
      address
      city
      country
      detailContent
      foundingYear
      headerImageUrl
      stripeCustomerId
      logoImageUrl
      latitude
      longitude
      totalEmployees
      dynamicLink
      createdAt
      updatedAt
    }
  }
`

export const GET_PAGINATED_COMPANIES = gql`
  query GetPaginatedCompanies($paginationInput: PaginationInput) {
    paginatedCompanies(paginationInput: $paginationInput) {
      items {
        name
        id
        address
        city
        country
        detailContent
        foundingYear
        headerImageUrl
        stripeCustomerId
        isFairManaged
        logoImageUrl
        latitude
        longitude
        totalEmployees
        dynamicLink
        createdAt
        updatedAt
      }
      meta {
        totalItems
        itemCount
        itemsPerPage
        totalPages
        currentPage
      }
    }
  }
`

export const GET_COMPANIES_FOR_FAIR_LIST = gql`
  query GetCompaniesForFairList {
    allCompanies {
      id
      name
      city
      address
      logoImageUrl
    }
  }
`

export const GET_COMPANIES_WITH_FAIR_STATUS = gql`
  query GetCompaniesWithFairStatus($fairId: String!) {
    allCompaniesWithFairStatus(fairId: $fairId) {
      id
      name
      city
      address
      logoImageUrl
      isInFair
      isFairManaged
    }
  }
`
