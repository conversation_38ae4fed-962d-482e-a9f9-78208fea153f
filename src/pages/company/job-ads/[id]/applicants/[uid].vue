<script setup lang="ts">
  import { useJobAdverts } from '@/composables/JobAdverts/useJobAdverts'
  import { useJobLikes } from '@/composables/JobAdverts/useJobLikes'
  import { useChatStore } from '@/stores/apiStores/useChatStore'
  import { useJobAdsStore } from '@/stores/jobAdsStore'
  import { useRoute, useRouter } from 'vue-router'

  const router = useRouter()
  const route = useRoute()

  const chatStore = useChatStore()
  const jobAdsStore = useJobAdsStore()

  const {
    state: { isPremium, jobAdvertTitle, jobAdId, loadingJobAdvert },
  } = useJobLikes()

  const {
    state: { jobAdText, jobAdvertLikes },
  } = useJobAdverts()

  const jobAdvertId = route.params?.id as string

  onMounted(() => {
    chatStore.init()
  })

  watchEffect(async () => {
    if (jobAdvertLikes.value) {
      await jobAdsStore.updateJobLikes(jobAdvertLikes.value, jobAdvertId)
    }
  })

  const showChatWindow = computed(() => {
    return chatStore.showChatWindow && chatStore.activeChat !== null
  })
</script>

<template>
  <div v-if="loadingJobAdvert">
    <v-skeleton-loader
      class="mx-auto border py-6"
      type="list-item-avatar-three-line"
    ></v-skeleton-loader>
  </div>
  <div v-else>
    <VRow v-if="!isPremium">
      <v-card
        elevation="0"
        class="mx-auto"
        prepend-icon="mdi-lock"
        rel="noopener"
        target="_blank"
        subtitle="Führen Sie ein Upgrade auf Premium durch, um Ihre Matches anzuzeigen und mit ihnen zu kommunizieren."
      >
        <template v-slot:title>
          <v-row no-gutters>
            <v-col cols="12" sm="9">
              <div class="text-h6">Dieser Job ist derzeit im Basisplan</div>
            </v-col>
            <v-col cols="12" sm="3">
              <v-btn
                append-icon="mdi-crown"
                @click="
                  router.push({
                    path: '/company/subscription/pricing',
                    query: { ad: jobAdId, tit: jobAdvertTitle },
                  })
                "
              >
                Upgrade to premium</v-btn
              >
            </v-col>
          </v-row>
        </template>
      </v-card>
    </VRow>
    <VRow v-else>
      <VCol cols="12">
        <ApplicantProfileHeader />
      </VCol>
      <VCol cols="6">
        <ApplicantProfile />
      </VCol>
      <VCol cols="6">
        <VCard class="py-1">
          <ChatWindow v-if="showChatWindow" :mini="true" />
          <VideoWindow v-else />
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>
