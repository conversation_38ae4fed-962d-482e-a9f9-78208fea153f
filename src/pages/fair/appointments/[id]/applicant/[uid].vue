<script setup lang="ts">
  import { useChatStore } from '@/stores/apiStores/useChatStore'
  import { useApplicantProfile } from '@/components/ApplicantProfile/hooks/useApplicantProfile'

  const chatStore = useChatStore()

  const {
    state: { statusArray, applicant, loadingApplicant },
  } = useApplicantProfile()

  onMounted(() => {
    chatStore.init()
  })

  const showChatWindow = computed(() => {
    return chatStore.showChatWindow && chatStore.activeChat !== null
  })
</script>

<template>
  <div>
    <div v-if="loadingApplicant">
      <v-skeleton-loader
        class="mx-auto border py-6"
        type="list-item-avatar-three-line"
      ></v-skeleton-loader>
    </div>
    <!-- Only render child components when data is available -->
    <div v-else-if="applicant && Object.keys(applicant).length > 0">
      <VRow>
        <VCol cols="12">
          <ApplicantProfileHeader />
        </VCol>
        <VCol cols="6">
          <ApplicantProfile />
        </VCol>
        <VCol cols="6">
          <VCard class="py-1">
            <ChatWindow v-if="showChatWindow" :mini="true" />
            <VideoWindow v-else />
          </VCard>
        </VCol>
      </VRow>
    </div>
    <!-- Show error message if data couldn't be loaded -->
    <div v-else class="text-center pa-4">
      <v-alert
        type="warning"
        title="No Data Available"
        text="Could not load applicant data. Please try again later."
      ></v-alert>
    </div>
  </div>
</template>

<route lang="yaml">
# @formatter:off
meta:
  super: true
  subject: auth
</route>
