import { setupLayouts } from 'virtual:generated-layouts'
import { createRouter, createWebHistory } from 'vue-router'

import routes from '~pages'
import { useAuthStore } from '@/stores/authStore'
import { initStores } from '@/stores/initStores'
import type { Actions, AppAbility, Subjects } from '@/plugins/casl/AppAbility'
import ability from '@/plugins/casl/ability'
import { useAppStore } from '@/stores/appStore'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/company',
    },
    ...setupLayouts(routes),
  ],
})

router.beforeEach(async to => {
  const authStore = useAuthStore()
  const isUserLoggedIn = authStore.user !== null && authStore.user.uid !== null
  const appStore = useAppStore()

  const allowedCompanyView = appStore.isInCompanyView && authStore.companyId

  if (to.meta.super === true) {
    if (isUserLoggedIn) return
    else return { name: 'super-login' }
  }

  // Public area, no permissions needed
  if (to.meta.public === true) return

  // User-auth related, non-public stores
  await initStores()

  if (to.meta.redirectIfLoggedIn && isUserLoggedIn) {
    if (authStore.companySetupDone || allowedCompanyView) {
      return '/company'
    } else {
      return { name: 'company-register-company' }
    }
  }

  if (
    !to.meta.action ||
    !to.meta.subject ||
    ability.can(to.meta.action as Actions, to.meta.subject as Subjects)
  ) {
    if (
      isUserLoggedIn &&
      !authStore.companySetupDone &&
      !allowedCompanyView &&
      to.name !== 'company-register-company'
    ) {
      console.log('got to registeration from here: ', {
        isUserLoggedIn: isUserLoggedIn,
        companySetupDone: authStore.companySetupDone,
        isInCompanyView: appStore.isInCompanyView,
      })
      return { name: 'company-register-company' }
    }
  } else {
    if (isUserLoggedIn) return { name: 'not-authorized' }
    else
      return {
        name: 'login',
        query: { to: to.name !== 'index' ? to.fullPath : undefined },
      }
  }
})

// Docs: https://router.vuejs.org/guide/advanced/navigation-guards.html#global-before-guards
export default router
