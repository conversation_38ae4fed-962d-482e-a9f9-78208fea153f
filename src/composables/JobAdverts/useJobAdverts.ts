import { useJobAdvertGraph } from '@/api/graphHooks/useJobAdvertGraph'
import { apolloClient } from '@/api/middleware/apolloClient'
import { useStaleImage } from '@/composables/StaleImage/useStaleImage'
import { usePusher } from '@/composables/usePusher'
import {
  cancelSubscriptionBeforeDelete,
  confirmJobAdApproval,
  confirmJobAdBlock,
  confirmJobAdPauseResume,
  confirmJobDeleteAlert,
  jobAdBlockedAlert,
  jobDeletedAlert,
  jobRejectedAlert,
} from '@/composables/useSweetAlert'
import { JobAdvert } from '@/gql/graphql'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { useJobAdFormStore } from '@/stores/job-ad-form/jobAdFormStore'
import { useJobAdsStore } from '@/stores/jobAdsStore'
import JobAd from '@/types/job-ad/job-ad'
import { inlineTranslate } from '@/utils/utils'
import applicants from '@images/svg/applicants.svg'
import impressions from '@images/svg/impressions.svg'
import likes from '@images/svg/likes.svg'
import matches from '@images/svg/matches.svg'
import { SweetAlertResult } from 'sweetalert2'
import { useRoute, useRouter } from 'vue-router'

interface Counts {
  LIKED: number
  DISLIKED: number
  BOOKMARKED: number
  MATCHED: number
  IMPRESSIONS: number

  [key: string]: number
}

interface StatJobAdvert {
  id: string
  counts: Counts
}

interface JobAdvertStats {
  likes: number
  bookmarks: number
  matches: number
  impressions: number
}

const addJobActionCounts = (jobAdverts: JobAdvert[]) => {
  if (!jobAdverts) {
    return []
  }

  return jobAdverts.map(advert => {
    const counts: Counts = {
      LIKED: 0,
      DISLIKED: 0,
      BOOKMARKED: 0,
      MATCHED: 0,
      IMPRESSIONS: 0,
    }

    let subscriptionPlan = 'BASIC'

    if (advert?.jobAction) {
      advert?.jobAction.forEach(action => {
        counts[action?.state]++
        counts.IMPRESSIONS++
      })
    }

    //Check if subscription has PREMIUM and return it, else return BASIC
    if (advert?.subscriptions && advert?.subscriptions.length > 0) {
      advert?.subscriptions.forEach(subscription => {
        if (subscription.plan === 'PREMIUM') {
          subscriptionPlan = 'PREMIUM'
        }
      })
    }

    return {
      ...advert,
      subscription: subscriptionPlan,
      counts,
    }
  })
}

export const useJobAdverts = () => {
  const authStore = useAuthStore()
  const isSuperUser = authStore.isSuperUser

  const {
    state: {
      jobAdsByCompany,
      loadingJobAdvertLikes: loadingLikes,
      allJobAds,
      paginatedJobAds,
      loadingAllJobAds,
      loadingPaginatedJobAds,
      jobAdvertLikes,
      loadingJobAdsByCompany: loadingJobAds,
      loadingJobAdStatsByCompany: loadingStatistics,
    },
    actions: {
      saveJobAdvert,
      refetchLikes,
      refetchJobAds,
      loadAllJobAds,
      loadPaginatedJobAds,
      loadJobAdsByCompany,
      loadJobAdStatsByCompany,
      updatePaginationParams,
      refetchAllJobAds,
      refetchPaginatedJobAds,
      refetchJobAdStatsByCompany,
      editJobAdvert,
      deleteJobAdvert,
      approveJobAd,
      blockJobAd,
      pauseJobAd,
      resumeJobAd,
    },
  } = useJobAdvertGraph()

  const {
    actions: { handleRemoveMultipleStaleImages },
  } = useStaleImage()

  const jobAdStore = useJobAdFormStore()
  const jobAdsStore = useJobAdsStore()
  const formStore = useJobAdFormStore()
  const appStore = useAppStore()
  const router = useRouter()

  const companyId = computed(() => authStore.companyId)

  const route = useRoute()
  const cid = computed(() => route.params.cid || companyId.value)
  const jid = computed(() => (route && route.params ? route.params.id : null))

  const { listen, unsubscribe } = usePusher()

  let cleanup: (() => void) | null = null

  const initiateJobAdsListen = async () => {
    if (authStore.isSuperUser) return
    console.log('We Listening')
    try {
      const privateChannelJobAdverts = `private-liveCompanyJobAdverts.${companyId.value}`

      cleanup = await listen(privateChannelJobAdverts, {
        event: 'jobAdvertUpdated',
        callback: async (data: any) => {
          try {
            await updateJobAdvertInCache(data, data?.id)
          } catch (error) {
            console.error('Error updating cache:', error)
          }
        },
      })
    } catch (error) {
      console.error('Error setting up listener:', error)
    }
  }

  const stopJobAdsListen = () => {
    if (cleanup) {
      cleanup()
      cleanup = null
      const privateChannelJobAdverts = `private-liveCompanyJobAdverts.${companyId.value}`
      unsubscribe(privateChannelJobAdverts)
    }
  }

  let companyActiveJobs = computed({
    get() {
      return addJobActionCounts(jobAdsByCompany.value)
    },
    set(jobs: JobAdvert[]) {
      // This setter is not used but required for the computed property
    },
  })

  const updateJobAdvertInCache = async (data: JobAdvert, jobAdId: string) => {
    try {
      apolloClient.cache.modify({
        id: apolloClient.cache.identify({
          __typename: 'JobAdvert',
          id: jobAdId,
        }),
        fields: {
          title(existingTitle) {
            return data.title ?? existingTitle
          },
          paused(existingPaused) {
            return data.paused ?? existingPaused
          },
          jobAction(existingJobAction) {
            return data.jobAction ?? existingJobAction
          },
          subscriptions(existingSubscriptions) {
            return data.subscriptions ?? existingSubscriptions
          },
          isDeclined(existingIsDeclined) {
            return data.isDeclined ?? existingIsDeclined
          },
          isDraft(existingIsDraft) {
            return data.isDraft ?? existingIsDraft
          },
          approved(existingApproved) {
            return data.approved ?? existingApproved
          },
        },
        optimistic: true,
      })
    } catch (error) {
      console.error('Error updating cache:', error)
    }
  }

  const calculateCompanyStats = (jobs: any[]): JobAdvertStats => {
    if (!jobs?.length) {
      return {
        likes: 0,
        bookmarks: 0,
        matches: 0,
        impressions: 0,
      }
    }

    return jobs.reduce(
      (stats, job) => ({
        likes: stats.likes + job.counts.LIKED,
        bookmarks: stats.bookmarks + job.counts.BOOKMARKED,
        matches: stats.matches + job.counts.MATCHED,
        impressions: stats.impressions + job.counts.IMPRESSIONS,
      }),
      { likes: 0, bookmarks: 0, matches: 0, impressions: 0 },
    )
  }

  const allJobAdsList = computed({
    get() {
      return addJobActionCounts(allJobAds.value)
    },
    set(jobs: JobAdvert[]) {},
  })

  const paginatedJobAdsList = computed({
    get() {
      return addJobActionCounts(paginatedJobAds.value?.items || [])
    },
    set(jobs: JobAdvert[]) {},
  })

  const allJobsApproved = computed(() => {
    return allJobAdsList.value.filter(job => job.approved)
  })

  const allJobsBlocked = computed(() => {
    return allJobAdsList.value.filter(job => !job.approved && job.isDeclined)
  })
  const allJobsPending = computed(() => {
    return allJobAdsList.value.filter(job => !job.approved && !job.isDeclined)
  })

  const comSats = computed(() => calculateCompanyStats(companyActiveJobs.value))

  const statistics = computed(() => [
    {
      title: inlineTranslate('Impressions'),
      icon: impressions,
      stats: comSats.value?.impressions,
    },
    {
      title: 'Bookmarks',
      icon: likes,
      stats: comSats.value?.bookmarks,
    },
    {
      title: inlineTranslate('Applicant'),
      icon: applicants,
      stats: comSats.value?.likes,
    },
    {
      title: 'Matches',
      icon: matches,
      stats: comSats.value?.matches,
    },
  ])

  const jobAdTypeOptions = [
    { label: 'Ausbildung', value: 0 },
    { label: 'Praktikum', value: 1 },
  ]

  const jobAdType = computed({
    get() {
      return jobAdTypeOptions.find(
        item => item.label.toLowerCase() === formStore.jobAdType,
      )?.value
    },
    set(type) {
      formStore.jobAdType = jobAdTypeOptions
        .find(item => item.value === type)
        ?.label.toLowerCase() as 'ausbildung' | 'praktikum'
    },
  })

  function getRedirectRoute(result: SweetAlertResult, jobAd: JobAd) {
    if (result.isConfirmed) {
      return {
        name: 'company-edit-job-ad-id',
        params: { id: jobAd.id },
      }
    }

    if (result.isDenied) {
      return {
        name: 'company-job-ads-id',
        params: { id: jobAd.id },
      }
    }

    return {
      name: 'company-job-ads-id',
      params: { id: jobAd.id },
    }
  }

  const handleRowClicked = async (e: any, jobAd: JobAd) => {
    const routePath =
      isSuperUser && !appStore?.isInCompanyView
        ? 'super-company-cid-job-ads-id'
        : 'company-job-ads-id'

    if (jobAd.isDraft) {
      await router.push({
        name: 'company-edit-job-ad-id',
        params: { id: jobAd?.id },
      })
      return
    }

    if (jobAd.declined) {
      const result = await jobRejectedAlert(jobAd)
      await router.push(getRedirectRoute(result, jobAd))
    } else {
      await router.push({
        name: routePath,
        params: { id: jobAd.id, cid: (cid.value as string) || jobAd.companyId },
      })
    }
  }

  const handleDeleteJobAd = async (
    jobAd: {
      id: string
      subscriptions: [{ status: string }]
    },
    dark: boolean,
  ) => {
    appStore.showAppLoader()
    try {
      if (
        jobAd.subscriptions &&
        jobAd.subscriptions.length > 0 &&
        jobAd.subscriptions[jobAd.subscriptions.length - 1].status !==
          'canceled'
      ) {
        await cancelSubscriptionBeforeDelete(dark)
      } else {
        const result = await confirmJobDeleteAlert(dark)
        if (result.isConfirmed) {
          appStore.hideAppLoader()
          await deleteJobAdvert({ id: jobAd.id })
          appStore.hideAppLoader()
          await refetchJobAds()
          await refetchJobAdStatsByCompany()
          jobDeletedAlert(dark)
        }
      }
    } catch (e) {
      console.error('Error deleting job ad:', e)
      appStore.hideAppLoader()
    } finally {
      appStore.hideAppLoader()
    }
  }

  const handleApproveJobAd = async (jobAdId: string, dark: boolean) => {
    const confirmApprove = await confirmJobAdApproval(dark)
    if (confirmApprove.isConfirmed) {
      appStore.showAppLoader()

      try {
        await approveJobAd({
          jobAdId,
        })
        await refetchSingleJobAd({
          jobAdvertId: jobAdId,
        })
        jobAdStore.setApprovedJobAd()
      } catch (error) {
        console.error('Error approving job ad:', error)
      } finally {
        appStore.hideAppLoader()
      }
    }
  }
  const handlePauseResume = async (
    jobAdId: string,
    dark: boolean,
    pause: boolean,
  ) => {
    const confirmPauseResume = await confirmJobAdPauseResume(dark, pause)
    if (confirmPauseResume.isConfirmed) {
      appStore.showAppLoader()

      try {
        if (pause) {
          await resumeJobAd({
            id: jobAdId,
          })
        } else {
          await pauseJobAd({
            id: jobAdId,
          })
        }
      } catch (error) {
        console.error('Error pausing or resuming job ad:', error)
      } finally {
        appStore.hideAppLoader()
      }
    }
  }

  const handleBlockJobAd = async (jobAdId: string, dark: boolean) => {
    const confirmBlock = await confirmJobAdBlock(dark)

    if (confirmBlock.isConfirmed) {
      appStore.showAppLoader()
      try {
        await blockJobAd({
          jobAdId,
          declineReason: confirmBlock.value,
        })
        jobAdStore.setBlockedJobAd()
      } catch (error) {
        console.error('Error blocking job ad:', error)
      } finally {
        appStore.hideAppLoader()
        jobAdBlockedAlert(dark)
      }
    }
  }

  const handleSaveJobAd = async (type = 'draft') => {
    const gehalt = jobAdStore?.gehalt
      ? [
          jobAdStore?.gehalt as number,
          jobAdStore.gehalt2 as number,
          jobAdStore.gehalt3 as number,
        ]
      : []

    const sanitizedGehalt = gehalt.map(value => value ?? 0)

    const newJobAd = {
      categoryIds: jobAdStore.selectedCategories,
      responsibleUsersIds: jobAdStore.selectedUsers,
      companyId: authStore.companyId,
      createJobAdvertInput: {
        title: jobAdStore.title,
        address: jobAdStore.address,
        isDraft: true,
        approved: false,
        city: jobAdStore.city,
        latitude: jobAdStore?.coordinates?.latitude,
        longitude: jobAdStore?.coordinates?.longitude,
        description: jobAdStore.description,
        workHours: jobAdStore.workHours,
        detailDescription: jobAdStore.detailDescription,
        district: jobAdStore.district,
        educationDuration: jobAdStore.educationDuration,
        gehalt: sanitizedGehalt,
        headerImageUrl: jobAdStore?.headerSavedUrl,
        holidayDays: jobAdStore.holidayDays,
        companyUserId: authStore?.claims?.companyUserId as string,
        startDate: jobAdStore?.startDate,
        activeFromDate: jobAdStore?.activeFromDate,
        type: jobAdStore?.jobAdType.toUpperCase(),
      },
    }

    const jobImgUrl = await jobAdsStore.setJobAdImage()
    if (jobImgUrl) newJobAd.createJobAdvertInput.headerImageUrl = jobImgUrl

    const createdJobAd = await saveJobAdvert(newJobAd)

    await handleRemoveMultipleStaleImages()

    await refetchJobAds()

    return createdJobAd
  }

  const handleEditJobAd = async () => {
    const gehalt = jobAdStore?.gehalt
      ? [
          jobAdStore?.gehalt as number,
          jobAdStore.gehalt2 as number,
          jobAdStore.gehalt3 as number,
        ]
      : []

    const responsibleUsersIdsToDisconnect =
      jobAdStore.originalSelectedUsers.filter(user => {
        return !jobAdStore.selectedUsers.includes(user)
      })

    const categoryIdsToDisconnect =
      jobAdStore.originalSelectedCategories.filter(category => {
        return !jobAdStore.selectedCategories.includes(category)
      })

    const editedJobAd = {
      id: (jobAdStore.id as string) || (jid.value as string),
      categoryIdsToConnect: jobAdStore.selectedCategories,
      categoryIdsToDisconnect: categoryIdsToDisconnect,
      responsibleUsersIdsToConnect: jobAdStore.selectedUsers,
      responsibleUsersIdsToDisconnect: responsibleUsersIdsToDisconnect,
      companyId: authStore.companyId || (cid.value as string),
      updateJobAdvertInput: {
        title: jobAdStore.title,
        address: jobAdStore.address,
        city: jobAdStore.city,
        latitude: jobAdStore?.coordinates?.latitude,
        longitude: jobAdStore?.coordinates?.longitude,
        description: jobAdStore.description,
        workHours: jobAdStore.workHours,
        detailDescription: jobAdStore.detailDescription,
        district: jobAdStore.district,
        educationDuration: jobAdStore.educationDuration,
        gehalt,
        headerImageUrl: jobAdStore?.headerSavedUrl,
        holidayDays: jobAdStore.holidayDays,
        companyUserId: authStore?.claims?.companyUserId as string,
        startDate: jobAdStore?.startDate,
        activeFromDate: jobAdStore?.activeFromDate,
        type: jobAdStore?.jobAdType.toUpperCase(),
      },
    }

    if (jobAdStore?.headerResult && jobAdStore?.headerResult?.imageBlob) {
      const jobImgUrl = await jobAdsStore.setJobAdImage()
      if (jobImgUrl) editedJobAd.updateJobAdvertInput.headerImageUrl = jobImgUrl
    }

    const editedJob = await editJobAdvert(editedJobAd)

    await handleRemoveMultipleStaleImages()

    return editedJob
  }

  const jobAdText = computed(() => ({
    jobType: inlineTranslate('Job Type'),
    jobTitle: inlineTranslate('Job Title'),
    internshipTitle: inlineTranslate('Internship Title'),
    companyAddress: inlineTranslate('Company Address'),
    regionTraining: inlineTranslate('Region Training'),
    regionInternship: inlineTranslate('Region Internship'),
    categoryTraining: inlineTranslate('Category Training'),
    categoryInternship: inlineTranslate('Category Internship'),
    hoursWeek: inlineTranslate('Hours / Week'),
    hours: inlineTranslate('Hours'),
    days: inlineTranslate('Days'),
    years: inlineTranslate('Years'),
    holidaysYear: inlineTranslate('Holidays / Year'),
    trainingDuration: inlineTranslate('Training Duration'),
    salary1Year: inlineTranslate('Salary 1st Year'),
    salary2Year: inlineTranslate('Salary 2nd Year'),
    salary3Year: inlineTranslate('Salary 3rd Year'),
    year2Optional: inlineTranslate('2nd Year (optional)'),
    year3Optional: inlineTranslate('3rd Year (optional)'),
    startOfTraining: inlineTranslate('Start of training'),
    adActiveFrom: inlineTranslate('Ad Active From'),
    whoShouldHaveAccess: inlineTranslate('Who should have access?'),
    titlePicture: inlineTranslate('Title Picture'),
    selectBanner: inlineTranslate('Select Banner'),
    availableFrom: inlineTranslate('Available From'),
    banner: inlineTranslate('Banner'),
    shortDescriptionTraining: inlineTranslate('Short Description Training'),
    briefDescriptionInternship: inlineTranslate('Brief Description Internship'),
    saveChanges: inlineTranslate('Save Changes'),
    approveJobAd: inlineTranslate('Approve Job Ad'),
    blockJobAd: inlineTranslate('Block Job Ad'),
    savePremium: inlineTranslate('Save Premium'),
    saveBasic: inlineTranslate('Save Basic'),
    saveDraft: inlineTranslate('Save Draft'),
    createJobAdvertisement: inlineTranslate('Create Job Advertisement'),
    createNewTraining: inlineTranslate('Create New Training'),
    editNewTraining: inlineTranslate('Edit New Training'),
    createNewInternship: inlineTranslate('Create New Internship'),
    editNewInternship: inlineTranslate('Edit New Internship'),
    createJobInfo: inlineTranslate('Create Job Info'),
    editJobInfo: inlineTranslate('Edit Job Info'),
    descriptionPlaceholder: inlineTranslate('Description Placeholder'),
    previewJobAd: inlineTranslate('Preview Job Ad Header'),
    previewJobAdInfo: inlineTranslate('Preview Job Ad Info'),
    trainingLocation: inlineTranslate('Training Location'),
    applicantsOverview: inlineTranslate('Overview Of Applicants'),
    statisticsOn: inlineTranslate('Statistics On'),
    realTimeData: inlineTranslate('Real Time Data'),
  }))

  return {
    state: {
      allJobAdsList,
      paginatedJobAdsList,
      paginatedJobAds,
      companyActiveJobs,
      jobAdvertLikes,
      allJobsApproved,
      allJobsBlocked,
      allJobsPending,
      loadingLikes,
      loadingJobAds,
      loadingStatistics,
      loadingAllJobAds,
      loadingPaginatedJobAds,
      statistics,
      jobAdTypeOptions,
      jobAdType,
      jobAdText,
    },
    actions: {
      handleRowClicked,
      handleDeleteJobAd,
      handleApproveJobAd,
      handlePauseResume,
      handleBlockJobAd,
      handleSaveJobAd,
      handleEditJobAd,
      saveJobAdvert,
      loadAllJobAds,
      loadPaginatedJobAds,
      updatePaginationParams,
      refetchAllJobAds,
      refetchPaginatedJobAds,
      refetchLikes,
      refetchJobAds,
      refetchJobAdStatsByCompany,
      calculateCompanyStats,
      updateJobAdvertInCache,
      initiateJobAdsListen,
      stopJobAdsListen,
      loadJobAdsByCompany,
      loadJobAdStatsByCompany,
    },
  }
}
