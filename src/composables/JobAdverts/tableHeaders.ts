import { inlineTranslate } from '@/utils/utils'
import { computed } from 'vue'

export const JobAdvertHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Title'),
      sortable: true,
      key: 'title',
    },
    {
      title: inlineTranslate('Impressions'),
      sortable: false,
      key: 'counts.IMPRESSIONS',
    },
    {
      title: inlineTranslate('Applicant'),
      sortable: false,
      key: 'counts.LIKED',
    },
    {
      title: 'Matches',
      sortable: false,
      key: 'counts.MATCHED',
    },
    {
      title: 'Status',
      sortable: false,
      key: 'approved',
    },
    {
      title: '',
      sortable: false,
      key: 'paused',
    },
    {
      title: 'Subscription',
      sortable: false,
      key: 'subscription',
    },
    {
      title: inlineTranslate('Actions'),
      sortable: false,
      key: 'actions',
    },
  ]
})

export const matchedApplicantHeaders = computed(() => {
  return [
    {
      title: inlineTranslate('Picture'),
      sortable: false,
      key: 'profileImageUrl',
    },
    {
      title: inlineTranslate('Name'),
      sortable: true,
      key: 'name',
    },
    {
      title: inlineTranslate('Age'),
      sortable: true,
      key: 'age',
    },
    {
      title: inlineTranslate('City'),
      sortable: true,
      key: 'city',
    },
    {
      title: inlineTranslate('Graduation'),
      sortable: true,
      key: 'graduation',
    },
    {
      title: 'Status',
      sortable: true,
      key: 'status',
    },
  ]
})
