import dayjs from 'dayjs'
import _ from 'lodash'
import { useAppStore } from '@/stores/appStore'
import { useContactPersonStore } from '@/stores/contact-person/contactPersonStore'
import { useContactPersonTimeslotGraph } from '@/api/graphHooks/useContactPersonTimeslotGraph'
import utc from 'dayjs/plugin/utc'

interface Timeslot {
  id: string
  startTime: string
  endTime: string
}

export const useContactPersonTimeslots = () => {
  const contactPersonStore = useContactPersonStore()
  const appStore = useAppStore()

  const activeContactPersonTimeslots = computed(() => {
    return contactPersonStore?.activeContactPersonTimeslots || []
  })

  const fairDays = computed(() => {
    return contactPersonStore?.activeContactPersonFairDays || []
  })

  dayjs.extend(utc)

  const formattedFairTimeslots = computed(() => {
    return activeContactPersonTimeslots.value
      .map((slot: any) => ({
        ...slot,
        formattedStart: dayjs.utc(slot.startTime).format('HH:mm'),
        formattedEnd: dayjs.utc(slot.endTime).format('HH:mm'),
        formattedDate: dayjs.utc(slot.startTime).format('DD. MMMM YYYY'),
        dateKey: dayjs.utc(slot.startTime).format('YYYY-MM-DD'),
      }))
      .sort((a: Timeslot, b: Timeslot) => {
        const dateCompare =
          dayjs.utc(a.startTime).valueOf() - dayjs.utc(b.startTime).valueOf()
        if (dateCompare !== 0) return dateCompare

        return dayjs
          .utc(a.startTime)
          .format('HH:mm')
          .localeCompare(dayjs.utc(b.startTime).format('HH:mm'))
      })
  })

  const groupedCPTimeslots = computed(() => {
    return _.groupBy(formattedFairTimeslots.value, 'dateKey')
  })

  const combinedFairDaysAndTimeslots = computed(() => {
    const groupedSlots = groupedCPTimeslots.value

    // Create a map to store the final grouped data
    const result = {} as any

    // Iterate through activeFairDays and ensure each date is included
    fairDays.value.forEach(day => {
      const dateKey = dayjs(day.startTime).format('YYYY-MM-DD')
      const formattedDate = dayjs(day.startTime).format('DD. MMMM YYYY') // Add formattedDate
      result[dateKey] = {
        formattedDate, // Include formattedDate in the result
        slots: groupedSlots[dateKey] || [], // Use existing slots or an empty array
      }
    })

    return result
  })

  const {
    state: { selectedTimeslot, loadingCreate, loadingUpdate, loadingRemove },
    actions: {
      createTimeslotMutation,
      updateTimeslotMutation,
      removeTimeslotMutation,
    },
  } = useContactPersonTimeslotGraph()

  const handleCreateCPTimeslot = async (formData: any) => {
    try {
      const input = {
        input: {
          companyFairContactPersonId:
            contactPersonStore?.companyFairContactPersonId,
          startTime: formData.startTime,
          endTime: formData.endTime,
          available: true,
        },
      }

      const data = await createTimeslotMutation(input)
      console.log('createTimeslotMutation')
      contactPersonStore.addContactPersonTimeslot({
        id: data?.data?.createContactPersonTimeslot?.id,
        ...formData,
        available: true,
      })
      return data
    } catch (err) {
      console.error('Error creating timeslot:', err)
      throw err
    }
  }

  const handleUpdateCPTimeslot = async (formData: any) => {
    try {
      if (!formData.id) {
        throw new Error('CPTimeslot ID is required for update')
      }

      const input = {
        input: {
          id: formData.id,
          startTime: formData.startTime,
          endTime: formData.endTime,
          available: formData.available,
        },
      }

      const data = await updateTimeslotMutation(input)
      contactPersonStore.setContactPersonTimeslot(formData)
      return data
    } catch (err) {
      console.error('Error updating timeslot:', err)
      throw err
    }
  }

  const handleRemoveCPTimeslot = async (id: string) => {
    try {
      const data = await removeTimeslotMutation({ id })
      contactPersonStore.removeContactPersonTimeslot(id)
      appStore.showSnack('Timeslot removed successfully')
      return data
    } catch (err) {
      console.error('Error removing timeslot:', err)
      throw err
    }
  }

  return {
    state: {
      formattedFairTimeslots,
      groupedCPTimeslots,
      combinedFairDaysAndTimeslots,
      loadingCreate,
      loadingUpdate,
      loadingRemove,
    },
    actions: {
      handleCreateCPTimeslot,
      handleUpdateCPTimeslot,
      handleRemoveCPTimeslot,
    },
  }
}
