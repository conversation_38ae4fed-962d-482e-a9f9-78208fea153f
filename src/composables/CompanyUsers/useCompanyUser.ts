import { useAuthGraph } from '@/api/graphHooks/useAuthGraph'
import { useCompanyUserGraph } from '@/api/graphHooks/useCompanyUserGraph'
import { companyUserSaveSuccess } from '@/composables/useSweetAlert'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { useUsersStore } from '@/stores/usersStore'
import ImageCropperResult from '@/types/image-cropper-result'
import { generateRandomString } from '@/utils/utils'

export const useCompanyUser = () => {
  interface User {
    id: string | null
    name: string
    email: string
    imageResult: ImageCropperResult
    rights: {
      superAdmin: boolean
      createJobAd: boolean
      editCompany: boolean
      createUser: boolean
    }
  }

  const {
    state: {
      companyUserInfo,
      companyUsersList,
      loadingCompanyUsers,
      loadingCompanyUser,
    },
    actions: {
      refetchCompanyUsers,
      refetchCompanyUser,
      updateCompanyUser,
      verifyCompanyUser,
      loadCompanyUsers,
      loadCompanyUser,
    },
  } = useCompanyUserGraph()

  const {
    actions: { createFirebaseBridgeUser },
  } = useAuthGraph()

  const authStore = useAuthStore()
  const usersStore = useUsersStore()
  const appStore = useAppStore()

  const companyUsers = computed(() => {
    return (
      companyUsersList.value.map(
        (user: {
          id: string
          avatarImageUrl: string
          name: string
          email: string
          userRights: object
        }) => ({
          id: user.id,
          avatarImageUrl: user.avatarImageUrl,
          name: user.name,
          email: user.email,
          rights: user.userRights,
        }),
      ) ?? []
    )
  })
  const createNewCompanyUser = async (user: User): Promise<string> => {
    try {
      appStore.showAppLoader()

      const dummyPassword = generateRandomString()
      const savedPhotoUrl = await usersStore.uploadUserAvatar(user.imageResult)

      const creds = {
        email: usersStore.userForm.email,
        password: dummyPassword,
        displayName: usersStore.userForm.name,
        avatarImageUrl: savedPhotoUrl || usersStore.userForm.avatarImageUrl,
      }

      const newCompanyUser = await createFirebaseBridgeUser({
        firebaseUser: creds,
        companyId: authStore.companyId,
        userRights: {
          createJobAd: usersStore.createJobAd,
          createUser: usersStore.createUser,
          editCompany: usersStore.editCompany,
          superAdmin: usersStore.superAdmin,
        },
      })

      appStore.hideAppLoader()

      if (newCompanyUser) {
        await refetchCompanyUsers()
        await companyUserSaveSuccess({
          name: user.name,
          password: dummyPassword,
          layoutVal: true,
        })
        usersStore.resetStore()
      }

      return newCompanyUser?.data?.createFirebaseBridgeUser?.id
    } catch (error) {
      appStore.hideAppLoader()
      console.error('Error creating company user:', error)
      return 'Error creating company user: ' + error
    }
  }
  const isUserAvailable = async (email: string) => {
    try {
      const companyUser = await verifyCompanyUser({
        email,
      })

      if (companyUser) {
        return true
      }
    } catch (error) {
      console.error(error)
      appStore.showSnack(
        `Entschuldigung, Benutzer mit der E-Mail-Adresse “${email}” wurde nicht gefunden. 😊`,
      )
      return false
    }
  }

  const handleUpdateCompanyUser = async (user: User): Promise<string> => {
    try {
      appStore.showAppLoader()

      const savedPhotoUrl = await usersStore.uploadUserAvatar(user.imageResult)

      const creds = {
        id: usersStore.userForm.id,
        name: usersStore.userForm.name,
        email: usersStore.userForm.email,
        avatarImageUrl: savedPhotoUrl || usersStore.userForm.avatarImageUrl,
      }

      const newCompanyUser = await updateCompanyUser({
        updateCompanyUserInput: creds,
        userRights: {
          createJobAd: usersStore.createJobAd,
          createUser: usersStore.createUser,
          editCompany: usersStore.editCompany,
          superAdmin: usersStore.superAdmin,
        },
      })

      appStore.hideAppLoader()

      if (newCompanyUser) {
        await refetchCompanyUsers()
        await companyUserSaveSuccess({
          name: user.name,
          password: '',
          layoutVal: true,
        })
        usersStore.resetStore()
        appStore.showSnack(`Benutzer wurde aktualisiert. 😊`)
      }

      return newCompanyUser?.data?.createFirebaseBridgeUser?.id
    } catch (error) {
      console.error('Error creating company user:', error)
      appStore.hideAppLoader()
      return 'Error creating company user: ' + error
    }
  }
  const updateActiveCompany = async (companyId: String) => {
    const updatedCompany = await updateCompanyUser({
      updateCompanyUserInput: {
        id: authStore.claims.companyUserId,
        activeCompanyId: companyId,
      },
    })
    console.log({ updatedCompany, companyId })
    return updatedCompany
  }

  return {
    state: {
      companyUsers,
      companyUserInfo,
      loadingCompanyUsers,
      loadingCompanyUser,
    },
    actions: {
      updateActiveCompany,
      isUserAvailable,
      createNewCompanyUser,
      handleUpdateCompanyUser,
      refetchCompanyUser,
      refetchCompanyUsers,
      loadCompanyUser,
      loadCompanyUsers,
    },
  }
}
