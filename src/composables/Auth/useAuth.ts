import { useAuthGraph } from '@/api/graphHooks/useAuthGraph'
import router from '@/router'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { useUsersStore } from '@/stores/usersStore'

export const useAuth = () => {
  const authStore = useAuthStore()
  const usersStore = useUsersStore()
  const appStore = useAppStore()

  const credentials = authStore.credentials

  const {
    state: { resetPasswordLoading },
    actions: { registerUser, saveCompanyUser, resetPassword },
  } = useAuthGraph()

  const handleSaveCompanyUser = async () => {
    return await saveCompanyUser()
  }

  const handleResetPassword = async (email: string) => {
    try {
      const resetPass = await resetPassword({ email })
      return resetPass
    } catch (error) {
      appStore.showSnack(
        `Entschuldigung, <PERSON><PERSON>er mit der E-Mail-Adresse “${email}” wurde nicht gefunden. 😊`,
      )
      console.error(error)
      throw new Error('error resetting password')
    }
  }

  const handleRegisterUser = async (): Promise<String> => {
    const { uid } = await authStore.registerUser(credentials)

    const newUser = await registerUser({
      registerInput: {
        firebaseUid: uid,
      },
    })

    const newUserId = newUser?.data?.register?.user?.id
    await authStore.setUserId(newUserId)

    const newCompanyUser = await saveCompanyUser({
      userId: newUserId,
      createCompanyUserInput: {
        name: credentials?.adminName,
        email: credentials?.email,
      },
    })

    if (newCompanyUser?.data?.createCompanyUser?.id) {
      authStore.credentials = {
        adminName: '',
        email: '',
        password: '',
        userId: newUserId,
        confirmPassword: '',
        confirmedPolicies: false,
      }

      usersStore.setActiveUser({
        rights: [{ superUser: true }],
        ...newCompanyUser?.data?.createFirebaseBridgeUser,
      })

      localStorage.setItem(
        'userDetails',
        JSON.stringify({
          userId: newUserId,
          companyUserId: newCompanyUser?.data?.createCompanyUser?.id,
        }),
      )
      await router.push({ name: 'company-register-company' })
    }
    return newCompanyUser?.data?.createCompanyUser?.id
  }

  return {
    state: { resetPasswordLoading },
    actions: {
      handleRegisterUser,
      handleSaveCompanyUser,
      handleResetPassword,
    },
  }
}
