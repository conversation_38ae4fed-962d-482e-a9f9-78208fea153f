import { useTimeslotGraph } from '@/api/graphHooks/useTimeslotGraph'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import _ from 'lodash'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/appStore'

interface Timeslot {
  id: string
  startTime: string
  endTime: string
}

export const useTimeslots = () => {
  const {
    state: {
      fairTimeslotsList,
      loadingAllTimeslots,
      loadingCreate,
      loadingUpdate,
      loadingRemove,
      loadingBulkRemove,
    },
    actions: {
      createTimeslot,
      updateTimeslot,
      removeTimeslot,
      removeMultipleTimeslots,
      refetchTimeslots,
      loadTimeslots,
    },
  } = useTimeslotGraph()

  const route = useRoute()

  const fairId = route.params.fid

  const appStore = useAppStore()

  dayjs.extend(utc)

  const formattedFairTimeslots = computed(() => {
    return fairTimeslotsList.value
      .map((slot: Timeslot) => ({
        ...slot,
        formattedStart: dayjs.utc(slot.startTime).format('HH:mm'),
        formattedEnd: dayjs.utc(slot.endTime).format('HH:mm'),
        formattedDate: dayjs.utc(slot.startTime).format('DD. MMMM YYYY'),
        dateKey: dayjs.utc(slot.startTime).format('YYYY-MM-DD'),
      }))
      .sort((a: Timeslot, b: Timeslot) => {
        const dateCompare =
          dayjs.utc(a.startTime).valueOf() - dayjs.utc(b.startTime).valueOf()
        if (dateCompare !== 0) return dateCompare

        return dayjs.utc(a.startTime)
          .format('HH:mm')
          .localeCompare(dayjs.utc(b.startTime).format('HH:mm'))
      })
  })

  const groupedFairTimeslots = computed(() => {
    return _.groupBy(formattedFairTimeslots.value, 'dateKey')
  })

  const handleCreateTimeslot = async (input: {
    startTime: string
    endTime: string
  }) => {
    try {
      console.log({
        input: {
          ...input,
          fairId,
        },
      })
      const response = await createTimeslot({
        input: {
          ...input,
          fairId,
        },
      })
      await refetchTimeslots()
      appStore.showSnack('Timeslot created')
      return response?.data.createTimeslot
    } catch (error) {
      console.error('Error creating timeslot:', error)
      appStore.showSnack("Couldn't create timeslot")
      throw error
    }
  }

  const handleUpdateTimeslot = async (
    id: string,
    input: { startTime: string; endTime: string },
  ) => {
    try {
      const response = await updateTimeslot({
        input: {
          id,
          ...input,
        },
      })
      await refetchTimeslots()
      return response?.data.updateTimeslot
    } catch (error) {
      console.error('Error updating timeslot:', error)
      throw error
    }
  }

  const handleRemoveTimeslot = async (id: string) => {
    try {
      const response = await removeTimeslot({
        id,
      })
      await refetchTimeslots()
      return response?.data.removeTimeslot
    } catch (error) {
      console.error('Error removing timeslot:', error)
      throw error
    }
  }

  const handleRemoveMultipleTimeslots = async (ids: string[]) => {
    try {
      const response = await removeMultipleTimeslots({
        ids,
      })
      await refetchTimeslots()
      appStore.showSnack(`${ids.length} timeslots deleted successfully`)
      return response?.data.removeMultipleTimeslots
    } catch (error) {
      console.error('Error removing multiple timeslots:', error)
      appStore.showSnack("Couldn't delete timeslots")
      throw error
    }
  }

  return {
    state: {
      formattedFairTimeslots,
      groupedFairTimeslots,
      loadingAllTimeslots,
      loadingCreate,
      loadingUpdate,
      loadingRemove,
      loadingBulkRemove,
    },
    actions: {
      handleCreateTimeslot,
      handleUpdateTimeslot,
      handleRemoveTimeslot,
      handleRemoveMultipleTimeslots,
      loadTimeslots,
    },
  }
}
