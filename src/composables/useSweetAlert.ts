import Swal from 'sweetalert2'
import { useTheme } from 'vuetify'
import jobAd from '@/types/job-ad/job-ad'

const confirmBtnClass =
  'v-btn v-btn--elevated bg-error v-btn--density-default v-btn--size-default v-btn--variant-elevated mr-3'
const cancelBtnClass =
  'v-btn v-btn--elevated v-btn--density-default v-btn--size-default v-btn--variant-outlined'

export const confirmJobDeleteAlert = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Sind Sie sicher?',
    text: 'Die Stellenanzeige wird unwiederruflich gelöscht. Bewerber, die diese Stelle geliked haben, werden informiert und können nicht mehr darauf zugreifen.',
    icon: 'warning',
    confirmButtonText: 'Ja, An<PERSON>ige löschen',
    cancelButtonText: 'Abbrechen',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const cancelSubscriptionBeforeDelete = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Bitte zunächst das Abo kündigen',
    text: "Um diese Stellenanzeige löschen zu können, beenden Sie bitte zunächst die dazugehörige Subscription im Menüpunkt 'Abrechnung'.",
    icon: 'warning',
    confirmButtonText: 'Ja, Anzeige löschen',
    cancelButtonText: 'Abbrechen',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const confirmSaveJobAd = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Sind Sie sicher?',
    text: 'Möchten Sie diese Stellenanzeige wirklich speichern?',
    icon: 'warning',
    confirmButtonText: 'Ja, Anzeige speichern',
    cancelButtonText: 'Abbrechen',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmEditJobAd = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Sind Sie sicher?',
    text: 'Möchten Sie diese Stellenanzeige wirklich bearbeiten?',
    icon: 'warning',
    confirmButtonText: 'Ja, Anzeige bearbeiten',
    cancelButtonText: 'Abbrechen',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const jobSaveSuccess = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Stellenanzeige erfolgreich gespeichert!',
    text: 'Der Job wurde erfolgreich gespeichert.',
    icon: 'success',
    confirmButtonText: 'Ok',
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const jobEditSuccess = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Stellenanzeige erfolgreich bearbeitet.',
    text: 'Der Job wurde erfolgreich bearbeitet.',
    icon: 'success',
    confirmButtonText: 'Ok',
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const newCompanySaveSuccess = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Ihr Unternehmen wurde erfolgreich angelegt.',
    text: 'Sie können nun mit dem Hinzufügen von Stellenanzeigen beginnen.',
    icon: 'success',
    confirmButtonText: 'OK',
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const passwordResetSuccess = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Passwort-Reset erfolgreich!',
    text: 'Bitte melden Sie sich mit Ihrem neuen Passwort an.',
    icon: 'success',
    confirmButtonText: 'Ok',
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const passwordResetEmailSent = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'E-Mail zum Zurücksetzen des Passworts gesendet',
    text: 'Eine E-Mail wurde an Ihre E-Mail gesendet. Klicken Sie auf den Link, um Ihr Passwort zurückzusetzen.',
    icon: 'success',
    confirmButtonText: 'Ok',
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const companyUserSaveSuccess = async (user: {
  name: string
  password: string
  layoutVal: boolean
}) => {
  return await Swal.fire({
    titleText: ` Unternehmensbenutzer: wurde erfolgreich erstellt.`,
    icon: 'success',
    confirmButtonText: 'Ok',
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: user.layoutVal ? 'dark' : 'light',
    },
  })
}

export const jobDeletedAlert = (layoutVal: boolean) => {
  Swal.fire({
    titleText: 'Anzeige gelöscht',
    text: 'Die Anzeige wurde erfolgreich entfernt und wird ab sofort nicht mehr ausgespielt.',
    confirmButtonText: 'OK',
    customClass: {
      confirmButton:
        'v-btn v-btn--elevated bg-success v-btn--density-default v-btn--size-default v-btn--variant-elevated mr-3',
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const jobRejectedAlert = async (
  jobAdvert?: jobAd,
  isDarkLayout = true,
) => {
  // @ts-ignore
  return await Swal.fire({
    titleText: 'Vorerst abgelehnt',
    html: `<p>Begründung: ${jobAdvert?.declineReason}</p><p>Wenn Sie die Stellenanzeige bearbeitet haben, wird sie automatisch erneut geprüft.</p>`,
    icon: 'warning',
    confirmButtonText: 'Anzeige bearbeiten',
    confirmButtonColor: 'success',
    denyButtonText: 'Zur Bewerberübersicht',
    showDenyButton:
      (jobAdvert?.applicants || 0) + (jobAdvert?.matches || 0) > 0 || false,
    buttonsStyling: false,
    showCloseButton: true,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: isDarkLayout ? 'dark' : 'light',
    },
  })
}

export const applicationRedrawnNotice = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Vom Bewerber gelöscht',
    text: 'Die Bewerbung wurde vom Schüler zurückgezogen. Möchten Sie diese Bewerbung löschen?',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ok',
    confirmButtonColor: 'success',
    cancelButtonText: 'Abbrechen',
    cancelButtonColor: 'primary',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmSubscriptionCancel = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Möchten Sie die Stellenanzeige wirklich nicht verlängern?',
    text: ' Ihre Stellenanzeige bleibt bis zum Ende des Abrechnungszyklus aktiv und wird danach nicht automatisch verlängert. Ab dann wird die Stellenanzeige nicht mehr ausgespielt. ',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ok',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const confirmApplicantDelete = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Are you sure you want to delete this applicant?',
    text: 'This applicant will be completely removed from the system.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ja',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const confirmSubscriptionRenew = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Renew this subscription?',
    text: 'The subscription will be charged at the end of the current billing period.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ja',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const subscriptionCancelledAlert = (layoutVal: boolean) => {
  Swal.fire({
    titleText: 'Subscription Cancelled',
    text: 'You have successfully cancelled the subscription and it will not be renewed.',
    confirmButtonText: 'OK',
    icon: 'success',
    customClass: {
      confirmButton:
        'v-btn v-btn--elevated bg-success v-btn--density-default v-btn--size-default v-btn--variant-elevated mr-3',
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const subscriptionRenewAlert = (layoutVal: boolean) => {
  Swal.fire({
    titleText: 'Subscription Renewed.',
    text: 'You have successfully renewed the subscription.',
    confirmButtonText: 'OK',
    icon: 'success',
    customClass: {
      confirmButton:
        'v-btn v-btn--elevated bg-success v-btn--density-default v-btn--size-default v-btn--variant-elevated mr-3',
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmJobAdApproval = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Approve Job Advert?',
    text: 'This will approve the job advert and make it visible to everyone.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ja',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const confirmJobAdPauseResume = async (
  layoutVal: boolean,
  pause: boolean,
) => {
  return await Swal.fire({
    titleText: pause
      ? 'Stellenanzeige aktivieren?'
      : 'Stellenanzeige pausieren?',
    text: pause
      ? 'Dadurch wird die Stellenanzeige fortgesetzt und für Bewerber sichtbar.'
      : 'Dadurch wird die Stellenanzeige pausiert und für Bewerber ausgeblendet.',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ja',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmJobAdBlock = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Stelle ablehnen',
    text: 'Die Stelle wird dem Unternehmen als abgelehnt angezeigt.',
    icon: 'warning',
    input: 'textarea',
    inputLabel: 'Begründung',
    showCancelButton: true,
    confirmButtonText: 'Ja',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const confirmMatchDecline = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Ablehnen bestätigen',
    text: 'Wenn Sie diesen Bewerber ablehnen, können Sie nicht mehr mit ihm kommunizieren. Sie haben die Möglichkeit eine Begründung anzugeben, die dem Bewerber angezeigt wird.',
    icon: 'warning',
    input: 'textarea',
    inputLabel: 'Begründung',
    showCancelButton: true,
    confirmButtonText: 'Ja',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmCompanyDelete = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Sind Sie sicher?',
    text: 'Dieses Unternehmen und alle seine Daten werden entfernt',
    icon: 'warning',
    confirmButtonText: 'Ja, Firma löschen',
    cancelButtonText: 'Abbrechen',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmContactPersonDelete = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Sind Sie sicher?',
    text: 'Diese Kontaktperson und alle ihre Daten werden gelöscht',
    icon: 'warning',
    confirmButtonText: 'Ja, Firma löschen',
    cancelButtonText: 'Abbrechen',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const confirmTimeslotDelete = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Sind Sie sicher?',
    text: 'Dieses Zeitfenster wird gelöscht',
    icon: 'warning',
    confirmButtonText: 'Ja, Firma löschen',
    cancelButtonText: 'Abbrechen',
    showCancelButton: true,
    buttonsStyling: false,
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const jobAdBlockedAlert = (layoutVal: boolean) => {
  Swal.fire({
    titleText: 'Abgelehnt!',
    text: 'Die Stellenanzeige wurde vorerst abgelehnt\n.',
    confirmButtonText: 'OK',
    icon: 'success',
    customClass: {
      confirmButton:
        'v-btn v-btn--elevated bg-success v-btn--density-default v-btn--size-default v-btn--variant-elevated mr-3',
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
export const jobAdBlockedNotice = (
  layoutVal: boolean,
  declineReason: string,
) => {
  Swal.fire({
    titleText: 'Die Anzeige wurde vorerst abgelehnt!',
    text: `Begründung: ${declineReason} \n . Wenn Sie die Stellenanzeige bearbeitet haben, wird sie erneut geprüft.`,
    confirmButtonText: 'OK',
    icon: 'warning',
    customClass: {
      confirmButton:
        'v-btn v-btn--elevated bg-warning v-btn--density-default v-btn--size-default v-btn--variant-elevated mr-3',
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const basicAdvertNotice = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Im Basic Plan nicht verfügbar',
    text: 'Um auf diesen Bewerber zugreifen zu können, muss die Stellenanzeige auf Premium hochgestuft werden',
    icon: 'warning',
    confirmButtonText: 'Ok',
    confirmButtonColor: 'success',
    cancelButtonText: 'Abbrechen',
    cancelButtonColor: 'primary',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const jobAdvertBlocked = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Derzeit nicht erreichbar',
    text: 'Sie können auf diesen Bewerber nicht zugreifen.',
    icon: 'warning',
    confirmButtonText: 'Ja',
    cancelButtonText: 'Abbrechen',
  })
}

export const confirmAppointmentReject = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Termin ablehnen',
    text: 'Wenn Sie diesen Termin ablehnen, wird der Bewerber benachrichtigt. Sie können eine Begründung angeben, die dem Bewerber angezeigt wird.',
    icon: 'warning',
    input: 'textarea',
    inputLabel: 'Begründung für die Ablehnung',
    inputPlaceholder: 'Geben Sie hier den Grund für die Ablehnung ein...',
    showCancelButton: true,
    confirmButtonText: 'Termin ablehnen',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}

export const confirmAppointmentDelete = async (layoutVal: boolean) => {
  return await Swal.fire({
    titleText: 'Termin löschen',
    text: 'Wenn Sie diesen Termin löschen, wird der Bewerber benachrichtigt. Sie können eine Begründung angeben, die dem Bewerber angezeigt wird.',
    icon: 'warning',
    input: 'textarea',
    inputLabel: 'Begründung für die Löschung',
    inputPlaceholder: 'Geben Sie hier den Grund für die Löschung ein...',
    showCancelButton: true,
    confirmButtonText: 'Termin löschen',
    cancelButtonText: 'Abbrechen',
    customClass: {
      confirmButton: confirmBtnClass,
      cancelButton: cancelBtnClass,
      popup: layoutVal ? 'dark' : 'light',
    },
  })
}
