import { useJobActionGraph } from '@/api/graphHooks/useJobActionGraph'
import router from '@/router'
import { useChatStore } from '@/stores/apiStores/useChatStore'
import { useJobAdsStore } from '@/stores/jobAdsStore'
import { useRoute } from 'vue-router'

type SetActionSuccess = {
  state: string
  chatId: string
}

export const useJobActions = () => {
  const route = useRoute()
  const jobAdvertId = route.params.id as string
  const applicantId = route.params.uid as string

  const jobAdsStore = useJobAdsStore()
  const chatStore = useChatStore()

  const jobAdLike = computed(() => {
    return (
      jobAdsStore.jobAdLikes[jobAdvertId]?.find(
        like => like.applicantId === applicantId,
      ) || null
    )
  })

  const jobActionIdFromRoute = jobAdLike.value?.id

  const {
    state: { isSettingJobAction, isDecliningMatchedApplicant },
    actions: {
      setJobAction,
      declineMatchedApplicant,
      onDeclineMatchedApplicant,
    },
  } = useJobActionGraph()

  const handleSetJobAction = async (): Promise<SetActionSuccess> => {
    try {
      const createUpdateInput = {
        applicantId,
        jobAdvertId,
        state: 'MATCHED',
        status: 'MATCHED',
        id: jobActionIdFromRoute,
      }

      const setData = await setJobAction({
        createOrUpdateJobActionInput: createUpdateInput,
      })

      chatStore.updateChatDetailsId(setData?.data?.setJobAction?.chatRoomId)

      return {
        state: 'success',
        chatId: setData?.data?.setJobAction?.chatRoomId,
      }
    } catch (error) {
      throw new Error('Error updating job action: ' + error)
    }
  }
  const handleUpdateStatus = async (status: string): Promise<string> => {
    try {
      const createUpdateInput = {
        applicantId,
        jobAdvertId,
        status: status,
        id: jobActionIdFromRoute,
      }

      await setJobAction({
        createOrUpdateJobActionInput: createUpdateInput,
      })

      return 'success'
    } catch (error) {
      console.error('Error updating job action:', error)
      return 'Error updating job action: ' + error
    }
  }

  const handleDeclineMatchedApplicant = async (
    jobActionId: string,
    declineReason?: string,
  ): Promise<string> => {
    try {
      await declineMatchedApplicant({
        jobActionId: jobActionId || jobActionIdFromRoute,
        declineReason: declineReason,
      })
      await jobAdsStore.removeJobLike(jobActionId, jobAdvertId)
      await router.push({
        name: 'company-job-ads-id',
        params: {
          id: jobAdvertId,
        },
      })

      return 'success'
    } catch (error) {
      console.error('Error declining matched applicant:', error)
      return 'Error declining matched applicant: ' + error
    }
  }

  return {
    state: {
      isSettingJobAction,
      isDecliningMatchedApplicant,
      jobAdLike,
    },
    actions: {
      handleDeclineMatchedApplicant,
      handleSetJobAction,
      onDeclineMatchedApplicant,
      handleUpdateStatus,
    },
  }
}
