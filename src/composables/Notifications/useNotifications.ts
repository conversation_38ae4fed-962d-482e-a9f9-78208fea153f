import { useNotificationGraph } from '@/api/graphHooks/useNotificationGraph'
import { usePusher } from '@/composables/usePusher'
import { useSound } from '@/composables/useSound'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { inlineTranslate } from '@/utils/utils'

export const useNotifications = () => {
  const {
    state: {
      userNotificationsList,
      loadingUserNotifications,
      loadingMarkAsRead,
      loadingMarkAllAsRead,
    },
    actions: { refetchUserNotifications, markAllAsRead, markAsRead, loadUserNotifications },
  } = useNotificationGraph()

  const authStore = useAuthStore()
  const appStore = useAppStore()

  const companyUserId = computed(() => authStore.claims.companyUserId)

  const { listen, unsubscribe } = usePusher()

  let cleanup: (() => void) | null = null

  const initiateNotificationsListen = async () => {
    if (authStore.isSuperUser || authStore.isFGAdmin) return
    try {
      const privateNotificationChannel = `private-liveNotifications.${companyUserId.value}`

      cleanup = await listen(privateNotificationChannel, {
        event: 'notification',
        callback: async (data: any) => {
          await appStore.pushToNotifications(data)
          appStore.showSnack(data.emailNotification?.emailBody)
        },
      })
    } catch (error) {
      console.error('Error setting up notifications listener:', error)
    }
  }

  const stopNotificationsListen = () => {
    if (cleanup) {
      cleanup()
      cleanup = null
      const privateNotificationChannel = `private-liveNotifications.${companyUserId.value}`
      unsubscribe(privateNotificationChannel)
    }
  }

  const handleMarkAllAsRead = async () => {
    return await markAllAsRead({
      companyUserId: companyUserId.value,
    })
  }
  const handleMarkAsRead = async (notificationId: string) => {
    return await markAsRead({
      notificationId: notificationId,
    })
  }

  const notificationsHeader = computed(() => {
    return [
      {
        title: inlineTranslate('Status'),
        sortable: true,
        key: 'isNew',
      },
      {
        title: inlineTranslate('Subject'),
        sortable: true,
        key: 'emailSubject',
      },
      {
        title: 'Nachricht',
        sortable: true,
        key: 'emailBody',
      },
      {
        title: 'Datum',
        sortable: true,
        key: 'time',
      },
      {
        title: inlineTranslate('Actions'),
        sortable: false,
        key: 'actions',
      },
    ]
  })

  return {
    state: {
      userNotificationsList,
      loadingUserNotifications,
      notificationsHeader,
      loadingMarkAsRead,
      loadingMarkAllAsRead,
    },
    actions: {
      refetchUserNotifications,
      handleMarkAllAsRead,
      handleMarkAsRead,
      initiateNotificationsListen,
      stopNotificationsListen,
      loadUserNotifications,
    },
  }
}
