import { Company } from '@/gql/graphql'
import { defineStore } from 'pinia'
import { useCompanyStore } from '../companyStore'
import type PlaceChanged from '@/types/place-changed'
import type ImageCropperResult from '@/types/image-cropper-result'

export const useCompanyFormStore = defineStore('companyFormStore', () => {
  const id = ref<string>()
  const companyName = ref<string>()
  const detailContent = ref<string>(
    '<h1>Detailbeschreibung</h1><p>Hier können Sie Ihr Unternehmen beschreiben und wichtige Aspekte auf den Punkt bringen. Sie haben folgende Text-Optionen: </p><ol><li>Aufzählungsliste</li><li><strong>Hervorgehobener Text</strong></li><li><em>Kursiver Text</em></li></ol>',
  )

  const address = ref<string>()
  const city = ref<string>()
  const mitarbeiter = ref<number>()
  const date = ref<number>()
  const position = ref<{
    lat: number
    long: number
  }>()
  const headerResult = ref<ImageCropperResult>()
  const logoResult = ref<ImageCropperResult>()
  const avatarResult = ref<ImageCropperResult>()
  const uploadedImages = ref<string[]>([])
  const imagesToDelete = ref<string[]>([])
  const staleImgIds = ref<string[]>([])
  const headerSavedUrl = ref<string>()
  const logoSavedUrl = ref<string>()
  const addressChanged = ref<boolean>()
  const initialSetup = ref<boolean>()

  function init(createNew: boolean, companyDetails: Company | null) {
    initialSetup.value = createNew

    if (!createNew && companyDetails) {
      try {
        id.value = companyDetails.id || ''
        companyName.value = companyDetails.name || ''
        detailContent.value = companyDetails?.detailContent || ''
        address.value = companyDetails.address || ''
        city.value = companyDetails.city || ''
        mitarbeiter.value = companyDetails.totalEmployees || 0
        date.value = companyDetails.foundingYear || 0
        position.value = {
          lat: companyDetails.latitude || 0,
          long: companyDetails.longitude || 0,
        }
        headerSavedUrl.value = companyDetails.headerImageUrl || ''
        logoSavedUrl.value = companyDetails.logoImageUrl || ''

        if (companyDetails.address) {
          placeChanged({
            address: companyDetails.address,
            city: companyDetails.city,
            coordinates: {
              lat: companyDetails.latitude || 0,
              long: companyDetails.longitude || 0,
            },
          })
        }
      } catch (error) {
        // Silent error handling
      }
    }
  }

  /**
   * Sets the city, address and geometry data from places autocomplete search
   * @param {Object} result Result from autocomplete
   * @param {String} result.city City name
   * @param {String} result.address Company address
   * @param {Object} result.coordinates LatLng Coordinates of place
   * @param {Number} result.coordinates.lat Latitude
   * @param {Number} result.coordinates.lng Longitude
   */
  function placeChanged(result: PlaceChanged) {
    address.value = result.address
    city.value = result.city
    position.value = {
      lat: result.coordinates.lat,
      long: result.coordinates.long,
    }
  }

  /**
   * Set cropped image result in state
   * @param {Object} result image result
   * @param {Blob} result.resultBlob Blob of cropped image
   * @param {String} result.resultUrl Result URL of cropped image (gets revoked after submitting the form)
   * @param {'profile'|'header'|'logo'} imageType
   */
  function imageChanged(
    result: ImageCropperResult,
    imageType: 'profile' | 'header' | 'logo',
  ) {
    console.log('imageChanged::', result, imageType)
    switch (imageType) {
      case 'profile':
        avatarResult.value = result
        break
      case 'header':
        headerResult.value = result
        break
      case 'logo':
        logoResult.value = result
        break
    }
  }

  async function saveImages(): Promise<boolean> {
    const companyStore = useCompanyStore()

    const res = await companyStore.uploadCompanyImages(
      {
        uploadedImages: uploadedImages.value,
        imagesToDelete: imagesToDelete.value,
        ...(headerResult.value != null && { headerResult: headerResult.value }),
        ...(logoResult.value != null && { logoResult: logoResult.value }),
        ...(initialSetup.value &&
          avatarResult.value != null && { avatarResult: avatarResult.value }),
      },
      initialSetup.value,
    )

    return true
  }

  function resetStore() {
    id.value = undefined
    companyName.value = undefined
    detailContent.value =
      '<h1>Detailbeschreibung</h1><p>Hier können Sie Ihr Unternehmen beschreiben und wichtige Aspekte auf den Punkt bringen. Sie haben folgende Text-Optionen: </p><ol><li>Aufzählungsliste</li><li><strong>Hervorgehobener Text</strong></li><li><em>Kursiver Text</em></li></ol>'
    address.value = undefined
    city.value = undefined
    mitarbeiter.value = undefined
    date.value = undefined
    position.value = undefined
    headerResult.value = undefined
    logoResult.value = undefined
    avatarResult.value = undefined
    uploadedImages.value = []
    imagesToDelete.value = []
    staleImgIds.value = []
    headerSavedUrl.value = undefined
    logoSavedUrl.value = undefined
    addressChanged.value = undefined
    initialSetup.value = undefined
  }

  return {
    companyName,
    detailContent,
    address,
    city,
    mitarbeiter,
    date,
    position,
    headerResult,
    logoResult,
    avatarResult,
    uploadedImages,
    imagesToDelete,
    headerSavedUrl,
    logoSavedUrl,
    staleImgIds,
    addressChanged,
    init,
    placeChanged,
    imageChanged,
    resetStore,
  }
})
