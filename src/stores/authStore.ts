/* eslint-disable indent */
import { apolloClient } from '@/api/middleware/apolloClient'
import type { User } from 'firebase/auth'
import {
  createUserWithEmailAndPassword,
  onIdTokenChanged,
  reload,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  updateProfile,
  confirmPasswordReset,
} from 'firebase/auth'
import type { Unsubscribe } from 'firebase/firestore'
import { defineStore } from 'pinia'
import { watch } from 'vue'
import { initStores, resetStores } from './initStores'
import ability, { initialAbility } from '@/plugins/casl/ability'
import { auth } from '@/libs/firebase/config'
import { sendEmailVerification } from 'firebase/auth'
import type { Credentials, PasswordReset } from '@/types/authTypes'
import router from '@/router'
import { useLoader } from '@/plugins/loading-overlay/loader'

let unsubAuthListener: Unsubscribe | null = null

/**
 * Store for auth related things
 */
export const useAuthStore = defineStore('authStore', {
  state: () => {
    return {
      /**
       * Auth user object containing uid, companyId and email
       */
      credentials: {} as Credentials,
      user: null as null | User,
      userId: null as null | string,
      stripeCustomerId: null as null | string,
      companyId: '' as string,
      claims: {} as Record<string, unknown>,
      companySetupDone: false,
      initialized: false,
    }
  },
  getters: {
    isLoggedIn: state => state.user != null && state.user.uid != null,
    isSuperUser: state => state.claims?.isSuperUser || false,
    isFGAdmin: state => state.claims?.isFGAdmin || false,
  },
  actions: {
    /**
     * Needs to be called before initializing Vue router and the Vue app.
     * In this way it is ensured to have the user object available if user is already logged in
     */
    async init() {
      return new Promise(resolve => {
        if (unsubAuthListener) unsubAuthListener()
        unsubAuthListener = onIdTokenChanged(auth, async user => {
          if (user != null) {
            const token = await user.getIdTokenResult(true)

            ability.update([
              ...(token.claims.createJobAd
                ? [
                    {
                      action: 'edit',
                      subject: 'JobAds',
                    },
                  ]
                : [
                    {
                      action: 'edit',
                      subject: 'JobAds',
                    },
                  ]),
              ...(token.claims.editCompany
                ? [
                    {
                      action: 'edit',
                      subject: 'company',
                    },
                  ]
                : [
                    {
                      action: 'edit',
                      subject: 'JobAds',
                    },
                  ]),
              ...(token.claims.createUser
                ? [
                    {
                      action: 'edit',
                      subject: 'users',
                    },
                  ]
                : [
                    {
                      action: 'edit',
                      subject: 'users',
                    },
                  ]),
              ...(token.claims.superAdmin
                ? [
                    {
                      action: 'manage',
                      subject: 'all',
                    },
                  ]
                : [
                    {
                      action: 'manage',
                      subject: 'all',
                    },
                  ]),
              {
                action: 'read',
                subject: 'auth',
              },
            ])

            localStorage.setItem('uid', token?.claims?.sub || '')

            if (!token.claims?.companyId && !token.claims.isSuperUser)
              this.companySetupDone = false
            else this.companySetupDone = true

            this.claims = token.claims
            this.user = user
            if (token.claims.companyId) {
              this.companyId = token.claims.companyId
            }
          } else {
            await resetStores()

            this.user = null
            this.companyId = ''
            ability.update(initialAbility)
          }
          this.initialized = true
          resolve(user)
        })
      })
    },

    async registerUser(credentials: Credentials): Promise<{ uid: string }> {
      try {
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          credentials.email,
          credentials.password,
        )

        await updateProfile(userCredential.user, {
          displayName: credentials.adminName,
        })
        console.log({ userCredential })

        return { uid: userCredential.user.uid }
      } catch (error) {
        console.log(error)
        return { uid: '' }
      }
    },

    async signInUser(credentials: Credentials) {
      try {
        const userCredential = await signInWithEmailAndPassword(
          auth,
          credentials.email,
          credentials.password,
        )

        await new Promise(resolve => {
          const stopWatch = watch(this.$state, () => {
            if (this.user?.uid === userCredential.user.uid) {
              initStores()
              resolve(null)
              stopWatch()
            }
          })
        })
        this.companySetupDone = true
      } catch (error) {
        console.log(error)
      }
    },

    async signOut() {
      console.log('signing out')
      // Create a fullscreen loader instance
      const loader = useLoader()

      const timeout = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Sign out timed out after 10 seconds'))
          loader.hide()
          router.push('/login')
        }, 10000)
      })

      try {
        // Show fullscreen loader
        loader.show()

        // Execute sign out process with a timeout
        await Promise.race([
          (async () => {
            // Reset all stores first
            await resetStores()

            // Reset Apollo client cache
            await apolloClient.resetStore()

            // Clear local storage
            localStorage.clear()

            // Sign out from Firebase
            await firebaseSignOut(auth)
          })(),
          timeout,
        ])

        await router.push('/login')
        return true
      } catch (error) {
        console.error('Error during sign out:', error)
        loader.hide()
        return false
      } finally {
        loader.hide()
      }
    },

    /**
     * Sends a request
     */
    async sendEmailVerification() {
      const currentUser = auth.currentUser
      if (currentUser) {
        sendEmailVerification(currentUser)
          .then(() => {
            console.log('Email verification sent')
          })
          .catch(error => {
            console.error('Error sending email verification:', error)
          })
      }
    },

    async resetPassword(resetCreds: PasswordReset) {
      const oobCode =
        new URL(window.location.href).searchParams.get('oobCode') || ''

      console.log({ oobCode })
      if (oobCode === '') return

      try {
        const resetPass = await confirmPasswordReset(
          auth,
          oobCode,
          resetCreds.password,
        )
        console.log({ resetPass })
      } catch (error) {
        console.log(error)
      }
    },
    async setUserId(userId: string) {
      this.credentials.userId = userId
    },

    async refreshUser() {
      if (this.user) await reload(this.user)
    },

    async setCompanyId(companyId: string) {
      this.$patch(state => {
        state.claims = {
          ...state.claims,
          companyId: companyId,
        }
        state.companyId = companyId
        state.companySetupDone = true
      })
    },

    async resetCompanyId() {
      this.companyId = ''
      this.claims.companyId = ''
      this.companySetupDone = false
    },
  },
  persist: {
    key: 'auth-store',
    storage: localStorage,

    paths: [
      'companyId',
      'claims',
      'companySetupDone',
      'initialized',
      'stripeCustomerId',
    ],

    // beforeRestore: context => {
    //   console.log('Restoring auth state:', context)
    // },
    // afterRestore: context => {
    //   console.log('Resore state done::', context.store.claims)
    // },
  },
})
