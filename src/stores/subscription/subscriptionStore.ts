import { useStripeSubscription } from '@/composables/StripeSubscription/useStripeSubscription'
import {
  PaymentMethodResponseDto,
  StripeCouponResponseDto,
  StripeCustomerInput,
  StripePromoCodeResponseDto,
} from '@/gql/graphql'
import { defineStore } from 'pinia'
import { useCompanyStore } from '@/stores/companyStore'

export const useSubscriptionStore = defineStore('subscriptionStore', {
  state: () => {
    return {
      paymentMethod: null as PaymentMethodResponseDto | null,
      customer: null as StripeCustomerInput | null,
      billingDetails: null as any,
      selectedMethod: null as string | null,
      jobAdverts: null as string[] | null,
      promoCode: null as string | null,
      couponDetails: null as StripeCouponResponseDto | null,
      promoCodeDetails: null as StripePromoCodeResponseDto | null,
    }
  },
  getters: {
    getPaymentMethod(): PaymentMethodResponseDto | null {
      return this.paymentMethod
    },
    getCustomer(): StripeCustomerInput | null {
      return this.customer
    },
    getJobAdverts(): string[] | null {
      return this.jobAdverts
    },
    getSelectedPaymentMethod(): any {
      return this.paymentMethod?.data?.find(
        method => method.id === this.selectedMethod,
      )
    },
    getCouponDetails(): StripeCouponResponseDto | null {
      return this.couponDetails
    },
    getPromoCodeDetails(): StripePromoCodeResponseDto | null {
      return this.promoCodeDetails
    },
  },
  actions: {
    async init() {
      const {
        state: { paymentMethods, stripeCustomer },
        actions: { refetchPaymentMethods },
      } = useStripeSubscription()

      const companyStore = useCompanyStore()

      const customerId =
        companyStore?.getCompany?.stripeCustomerId ||
        companyStore?.company?.stripeCustomerId ||
        stripeCustomer.value?.id

      if (customerId) {
        await refetchPaymentMethods()

        if (paymentMethods.value) {
          // @ts-ignore
          this.paymentMethod = paymentMethods.value
        }
      }
    },
    updatePaymentMethod(paymentMethod: PaymentMethodResponseDto) {
      this.paymentMethod = paymentMethod
    },

    updateSelectedMethod(selectedMethod: string) {
      console.log({ selectedMethod })
      this.selectedMethod = selectedMethod
    },

    async updateJobAdverts(jobAdverts: string[]) {
      this.jobAdverts = jobAdverts
    },
    updateCouponDetails(couponDetails: StripeCouponResponseDto) {
      this.couponDetails = couponDetails
    },
    updatePromoCodeDetails(promoCodeDetails: StripePromoCodeResponseDto) {
      this.promoCodeDetails = promoCodeDetails
    },
    removePromoCode() {
      this.couponDetails = null
    },
    resetStore() {
      this.paymentMethod = null
      this.customer = null
      this.billingDetails = null
      this.selectedMethod = null
      this.jobAdverts = null
      this.promoCode = null
      this.couponDetails = null
    },
  },
  persist: true,
})
