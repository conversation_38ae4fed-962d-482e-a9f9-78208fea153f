#!/bin/bash

# Usage: ./deploy.sh [develop|public]

if [ "$1" = "dev" ]; then
  site="develop-bridge"
  env="dev"
elif [ "$1" = "prod" ]; then
  site="bridge-public"
  env="prod"
else
  echo "Usage: ./firebase-deploy.sh [dev|prod]"
  exit 1
fi

# Build first
echo "Building for $env environment..."
yarn build:$env

# Create temporary firebase config
echo "Creating firebase.json for $site"
cat > firebase.json << EOF
{
  "hosting": {
    "public": "dist",
    "site": "$site",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
EOF

# Deploy
firebase deploy --only hosting:$site
[{"__typename":"StripeSubscription","amountTotal":5000,"stripeCustomerId":"cus_RPVNhPl1DEM1UW","stripeSubscriptionId":"sub_1QtRK7G9yclWrwpcZfzIkVlH","checkoutSessionId":"sub_1QtRK7G9yclWrwpcZfzIkVlH","cancelAtPeriodEnd":false,"currency":"eur","invoiceId":"in_1QtRK7G9yclWrwpcSESWl0CM","percent_off":0,"isActive":true,"expiresAt":"2025-08-17T10:13:11.000Z","paymentStatus":"succeeded","jobAdvert":{"__typename":"JobAdvert","title":"Hello JOb"}},{"__typename":"StripeSubscription","amountTotal":5000,"stripeCustomerId":"cus_RPVNhPl1DEM1UW","stripeSubscriptionId":"sub_1QtRy9G9yclWrwpcek3CpKGj","checkoutSessionId":"pi_3QtRy9G9yclWrwpc1fR1neq7","cancelAtPeriodEnd":false,"currency":"eur","invoiceId":"in_1QtRy9G9yclWrwpcN4xju6em","percent_off":0,"isActive":true,"expiresAt":"2025-08-17T10:54:33.000Z","paymentStatus":"succeeded","jobAdvert":{"__typename":"JobAdvert","title":"Jira Jobs"}},{"__typename":"StripeSubscription","amountTotal":5000,"stripeCustomerId":"cus_RPVNhPl1DEM1UW","stripeSubscriptionId":"sub_1QtS1rG9yclWrwpcj0mYqeSf","checkoutSessionId":"sub_1QtS1rG9yclWrwpcj0mYqeSf","cancelAtPeriodEnd":false,"currency":"eur","invoiceId":"in_1QtS1rG9yclWrwpcaGRE7YZA","percent_off":0,"isActive":true,"expiresAt":"2025-08-17T10:58:23.000Z","paymentStatus":"succeeded","jobAdvert":{"__typename":"JobAdvert","title":"Draft test"}},{"__typename":"StripeSubscription","amountTotal":5000,"stripeCustomerId":"cus_RPVNhPl1DEM1UW","stripeSubscriptionId":"sub_1QtS1sG9yclWrwpcVo2BpwM1","checkoutSessionId":"sub_1QtS1sG9yclWrwpcVo2BpwM1","cancelAtPeriodEnd":false,"currency":"eur","invoiceId":"in_1QtS1sG9yclWrwpcQrg2VU5I","percent_off":0,"isActive":true,"expiresAt":"2025-08-17T10:58:24.000Z","paymentStatus":"succeeded","jobAdvert":{"__typename":"JobAdvert","title":"PIpe"}},{"__typename":"StripeSubscription","amountTotal":5000,"stripeCustomerId":"cus_RPVNhPl1DEM1UW","stripeSubscriptionId":"sub_1QtSQDG9yclWrwpchjjvHeso","checkoutSessionId":"sub_1QtSQDG9yclWrwpchjjvHeso","cancelAtPeriodEnd":false,"currency":"eur","invoiceId":"in_1QtSQDG9yclWrwpc9ZhsD82c","percent_off":0,"isActive":true,"expiresAt":"2025-08-17T11:23:33.000Z","paymentStatus":"succeeded","jobAdvert":{"__typename":"JobAdvert","title":"Listerilne Job"}},{"__typename":"StripeSubscription","amountTotal":5000,"stripeCustomerId":"cus_RPVNhPl1DEM1UW","stripeSubscriptionId":"sub_1QtSRGG9yclWrwpc9tqiRwbU","checkoutSessionId":"sub_1QtSRGG9yclWrwpc9tqiRwbU","cancelAtPeriodEnd":false,"currency":"eur","invoiceId":"in_1QtSRGG9yclWrwpc29bbBPXd","percent_off":0,"isActive":true,"expiresAt":"2025-08-17T11:24:38.000Z","paymentStatus":"succeeded","jobAdvert":{"__typename":"JobAdvert","title":"Talk here"}},{"__typename":"StripeSubscription","amountTotal":5000,"stripeCustomerId":"cus_RPVNhPl1DEM1UW","stripeSubscriptionId":"sub_1QtT6wG9yclWrwpcIs6Gn3xL","checkoutSessionId":"sub_1QtT6wG9yclWrwpcIs6Gn3xL","cancelAtPeriodEnd":false,"currency":"eur","invoiceId":"in_1QtT6wG9yclWrwpcWfR2OwoS","percent_off":0,"isActive":true,"expiresAt":"2025-08-17T12:07:42.000Z","paymentStatus":"succeeded","jobAdvert":{"__typename":"JobAdvert","title":"Jira Jobs"}}]
