/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AddFairJobDialog: typeof import('./src/components/Fairs/FairJobs/AddFairJobDialog.vue')['default']
    AdvancedSetting: typeof import('./src/components/Chat/Agora/AdvancedSetting/index.vue')['default']
    AgoraVideoCallWindow: typeof import('./src/components/Chat/AgoraVideoCallWindow.vue')['default']
    AgoraVideoPlayer: typeof import('./src/components/Chat/Agora/AgoraVideoPlayer/index.vue')['default']
    AllApplicantsTable: typeof import('./src/components/Applicant/AllApplicantsTable.vue')['default']
    AllCompanies: typeof import('./src/components/Company/AllCompanies.vue')['default']
    AllFairJobsTable: typeof import('./src/components/Fairs/FairJobs/AllFairJobsTable.vue')['default']
    AllJobAdverts: typeof import('./src/components/JobAdvert/AllJobAdverts.vue')['default']
    AppBarSearch: typeof import('./src/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./src/@core/components/AppCardActions.vue')['default']
    AppDateTimePicker: typeof import('./src/@core/components/AppDateTimePicker.vue')['default']
    ApplicantProfile: typeof import('./src/components/ApplicantProfile/ApplicantProfile.vue')['default']
    ApplicantProfileHeader: typeof import('./src/components/ApplicantProfile/ApplicantProfileHeader.vue')['default']
    AppointmentsFilter: typeof import('./src/components/Fairs/Appointments/AppointmentsFilter.vue')['default']
    AppointmentsFilterOne: typeof import('./src/components/Fairs/Appointments/AppointmentsFilterOne.vue')['default']
    AppointmentsTable: typeof import('./src/components/Fairs/Appointments/AppointmentsTable.vue')['default']
    AppOtpInput: typeof import('./src/@core/components/AppOtpInput.vue')['default']
    AppPricing: typeof import('./src/@core/components/AppPricing.vue')['default']
    AppSearchHeader: typeof import('./src/@core/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./src/@core/components/AppSelect.vue')['default']
    AppTextField: typeof import('./src/@core/components/AppTextField.vue')['default']
    BillingCustomerDetails: typeof import('./src/components/Subscription/BillingCustomerDetails.vue')['default']
    BillingCustomerForm: typeof import('./src/components/Subscription/BillingCustomerForm.vue')['default']
    BreadCrumbs: typeof import('./src/components/BreadCrumbs.vue')['default']
    CameraSelect: typeof import('./src/components/Chat/Agora/CameraSelect/index.vue')['default']
    CardAddEditDialog: typeof import('./src/@core/components/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./src/@core/components/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./src/@core/components/CardStatisticsVerticalSimple.vue')['default']
    ChatActiveChatUserProfileSidebarContent: typeof import('./src/components/Chat/ChatActiveChatUserProfileSidebarContent.vue')['default']
    ChatContact: typeof import('./src/components/Chat/ChatContact.vue')['default']
    ChatLeftSidebarContent: typeof import('./src/components/Chat/ChatLeftSidebarContent.vue')['default']
    ChatLog: typeof import('./src/components/Chat/ChatLog.vue')['default']
    ChatUserProfileSidebarContent: typeof import('./src/components/Chat/ChatUserProfileSidebarContent.vue')['default']
    ChatWindow: typeof import('./src/components/Chat/ChatWindow.vue')['default']
    CheckoutDetails: typeof import('./src/components/Subscription/CheckoutDetails.vue')['default']
    CloudProxySelect: typeof import('./src/components/Chat/Agora/CloudProxySelect/index.vue')['default']
    CodecSelect: typeof import('./src/components/Chat/Agora/CodecSelect/index.vue')['default']
    CompanyAppointmentsTable: typeof import('./src/components/Fairs/Appointments/CompanyAppointmentsTable.vue')['default']
    CompanyCategoryStrip: typeof import('./src/components/Fairs/Company/CompanyCategoryStrip.vue')['default']
    CompanyFairJobDialog: typeof import('./src/components/Fairs/Company/CompanyFairJobDialog.vue')['default']
    CompanyFairJobsTable: typeof import('./src/components/Fairs/Company/CompanyFairJobsTable.vue')['default']
    CompanyFairTable: typeof import('./src/components/Fairs/CompanyFairTable.vue')['default']
    CompanyForm: typeof import('./src/components/Company/CompanyForm.vue')['default']
    CompanyList: typeof import('./src/components/Fairs/CompanyList.vue')['default']
    CompanyPartnerLinks: typeof import('./src/components/Fairs/Company/CompanyPartnerLinks.vue')['default']
    CompanySwitchDialog: typeof import('./src/components/CompanySwitchDialog/CompanySwitchDialog.vue')['default']
    ConfirmDialog: typeof import('./src/@core/components/ConfirmDialog.vue')['default']
    ContactPersonDialog: typeof import('./src/components/Fairs/Company/ContactPersonDialog.vue')['default']
    ContactPersonsList: typeof import('./src/components/Fairs/Company/ContactPersonsList.vue')['default']
    ContactPersonsTable: typeof import('./src/components/Fairs/Company/ContactPersonsTable.vue')['default']
    ContactPersonTimeslot: typeof import('./src/components/Fairs/Company/ContactPersonTimeslot.vue')['default']
    CustomizerSection: typeof import('./src/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./src/components/CustomRadios.vue')['default']
    DefaultLayoutWithVerticalNav: typeof import('./src/layouts/components/DefaultLayoutWithVerticalNav.vue')['default']
    DefaultTimeslots: typeof import('./src/components/Fairs/Company/DefaultTimeslots.vue')['default']
    DialogCloseBtn: typeof import('./src/@core/components/DialogCloseBtn.vue')['default']
    EditAddressDialog: typeof import('./src/@core/components/EditAddressDialog.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./src/@core/components/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./src/@core/components/ErrorHeader.vue')['default']
    FairBanner: typeof import('./src/components/Fairs/FairBanner.vue')['default']
    FairCloneList: typeof import('./src/components/Fairs/FairCloneList.vue')['default']
    FairCompanysTable: typeof import('./src/components/Fairs/Company/FairCompanysTable.vue')['default']
    FairForm: typeof import('./src/components/Fairs/FairForm.vue')['default']
    FairJobsList: typeof import('./src/components/Fairs/Company/FairJobsList.vue')['default']
    FairStatsStrip: typeof import('./src/components/FairStatsStrip.vue')['default']
    FairTable: typeof import('./src/components/Fairs/FairTable.vue')['default']
    FileInput: typeof import('./src/components/Chat/Agora/FileInput/index.vue')['default']
    Header: typeof import('./src/components/Chat/Agora/Header/index.vue')['default']
    I18n: typeof import('./src/@core/components/I18n.vue')['default']
    ImageInputCrop: typeof import('./src/components/ImageInputCrop.vue')['default']
    JobAdForm: typeof import('./src/components/JobAdvert/JobAdForm.vue')['default']
    JobAdPreview: typeof import('./src/components/JobAdvert/JobAdPreview.vue')['default']
    JobAdvertLinkDialog: typeof import('./src/components/JobAdvertLinkDialog/JobAdvertLinkDialog.vue')['default']
    JobAdvertTable: typeof import('./src/components/JobAdvert/JobAdvertTable.vue')['default']
    JobLikesTable: typeof import('./src/components/JobAdvert/JobLikesTable.vue')['default']
    JobStatistics: typeof import('./src/components/JobStatistics.vue')['default']
    JoinForm: typeof import('./src/components/Chat/Agora/JoinForm/index.vue')['default']
    LanguageSwitcher: typeof import('./src/components/LanguageSwitcher.vue')['default']
    ManageFairStrip: typeof import('./src/components/Fairs/Company/ManageFairStrip.vue')['default']
    MediaDeviceTest: typeof import('./src/components/Chat/Agora/MediaDeviceTest/index.vue')['default']
    MicrophoneSelect: typeof import('./src/components/Chat/Agora/MicrophoneSelect/index.vue')['default']
    MoreBtn: typeof import('./src/components/MoreBtn.vue')['default']
    NavBarNotifications: typeof import('./src/layouts/components/NavBarNotifications.vue')['default']
    NavbarShortcuts: typeof import('./src/layouts/components/NavbarShortcuts.vue')['default']
    NavbarThemeSwitcher: typeof import('./src/layouts/components/NavbarThemeSwitcher.vue')['default']
    NetworkTestDialog: typeof import('./src/components/Chat/Agora/NetworkTestDialog/index.vue')['default']
    NewCatStrip: typeof import('./src/components/Fairs/Company/NewCatStrip.vue')['default']
    NotAuthorized: typeof import('./src/components/NotAuthorized.vue')['default']
    Notifications: typeof import('./src/@core/components/Notifications.vue')['default']
    NotificationsTable: typeof import('./src/components/Notifications/NotificationsTable.vue')['default']
    PaymentForm: typeof import('./src/components/Subscription/paymentForm.vue')['default']
    PaymentMethods: typeof import('./src/components/Subscription/PaymentMethods.vue')['default']
    PlacesAutocomplete: typeof import('./src/components/PlacesAutocomplete.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Shortcuts: typeof import('./src/@core/components/Shortcuts.vue')['default']
    SrMessages: typeof import('./src/components/Subscription/SrMessages.vue')['default']
    StatusChip: typeof import('./src/components/JobAdvert/statusChip.vue')['default']
    StripeCustomerDialog: typeof import('./src/components/Subscription/StripeCustomerDialog.vue')['default']
    SubscriptionTable: typeof import('./src/components/Subscription/SubscriptionTable.vue')['default']
    TextEditor: typeof import('./src/components/TextEditor/TextEditor.vue')['default']
    TheCustomizer: typeof import('./src/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./src/@core/components/ThemeSwitcher.vue')['default']
    TimeslotAvailability: typeof import('./src/components/Fairs/Company/TimeslotAvailability.vue')['default']
    Timeslots: typeof import('./src/components/Fairs/Timeslots.vue')['default']
    UserDrawer: typeof import('./src/components/UserDrawer.vue')['default']
    UserInfoEditDialog: typeof import('./src/@core/components/UserInfoEditDialog.vue')['default']
    UserProfile: typeof import('./src/layouts/components/UserProfile.vue')['default']
    UserUpgradePlanDialog: typeof import('./src/@core/components/UserUpgradePlanDialog.vue')['default']
    VerifyEmail: typeof import('./src/components/VerifyEmail.vue')['default']
    VideoProfileSelect: typeof import('./src/components/Chat/Agora/VideoProfileSelect/index.vue')['default']
    VideoWindow: typeof import('./src/components/Chat/VideoWindow.vue')['default']
  }
}
