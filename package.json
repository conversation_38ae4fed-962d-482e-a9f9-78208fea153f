{"name": "bridge", "version": "1.0.1", "scripts": {"dev": "npm run build:icons && vite --host", "dev:loc": "npm run build:icons && vite --host --mode loc", "dev:dev": "npm run build:icons && vite --host --mode development", "dev:prod": "npm run build:icons && vite --host --mode production", "build": "npm run build:icons && vite build", "build:dev": "npm run build:icons && vite build --mode development", "build:prod": "npm run build:icons && vite build --mode production", "build-deploy-dev": "npm run build:icons && vite build && firebase deploy --only hosting:bridge-dev", "build-deploy-prod": "npm run build:icons && vite build && firebase deploy --only hosting", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . -c .eslintrc.js --fix --rulesdir eslint-internal-rules/ --ext .ts,.js,.vue,.tsx,.jsx", "build:icons": "tsc -b src/@iconify && node src/@iconify/build-icons.js", "codegen": "npx graphql-codegen"}, "dependencies": {"@apollo/client": "^3.8.8", "@casl/ability": "^6.5.0", "@casl/vue": "^2.2.1", "@floating-ui/dom": "1.0.4", "@intlify/unplugin-vue-i18n": "^1.5.0", "@stripe/stripe-js": "^1.22.0", "@tabler/icons": "^1.97.0", "@vue/apollo-composable": "^4.0.0-beta.12", "@vueuse/core": "^9.5.0", "@vueuse/math": "^9.5.0", "agora-rtc-sdk-ng": "latest", "agora-rtc-vue": "^0.2.45", "apollo-cache-inmemory": "^1.6.6", "apollo-client": "^2.6.10", "apollo-link-http": "^1.5.17", "axios": "^1.6.1", "axios-mock-adapter": "^1.21.2", "chart.js": "^3.9.1", "date-fns": "^3.6.0", "destr": "^2.0.2", "element-plus": "^2.7.2", "firebase": "^9.17.1", "geofirestore-core": "^5.0.0", "graphql": "^15.8.0", "graphql-tag": "^2.12.6", "graphql-ws": "^5.15.0", "howler": "^2.2.4", "jspdf": "^2.5.1", "lodash.clonedeep": "^4.5.0", "pinia": "^2.1.3", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "^3.2.1", "pusher-js": "^8.4.0-rc2", "qrcode": "^1.5.1", "quill": "^2.0.2", "quill-image-uploader": "^1.3.0", "sass": "1.77.6", "sweetalert2": "^11.7.2", "uniqid": "^5.4.0", "unplugin-vue-define-options": "^1.4.2", "vue": "^3.4.19", "vue-advanced-cropper": "^2.8.8", "vue-chartjs": "^4.1.1", "vue-confetti": "^2.3.0", "vue-flatpickr-component": "11.0.1", "vue-i18n": "^9.2.2", "vue-prism-component": "^2.0.0", "vue-rewards": "^1.0.1", "vue-router": "^4.1.5", "vue3-loading-overlay": "^0.0.0", "vue3-perfect-scrollbar": "^1.6.0", "vuetify": "3.5.5", "webfontloader": "^1.6.28"}, "devDependencies": {"@antfu/eslint-config-vue": "^0.31.0", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/client-preset": "^4.1.0", "@graphql-codegen/typescript-vue-apollo": "^4.1.0", "@iconify-json/fa": "^1.1.2", "@iconify-json/tabler": "^1.1.35", "@iconify/icons-mdi": "^1.2.43", "@iconify/tools": "^2.1.0", "@iconify/vue": "^4.0.0", "@storipress/apollo-vue-devtool": "^0.0.4", "@types/google.maps": "^3.52.2", "@types/howler": "^2.2.12", "@types/lodash.clonedeep": "^4.5.7", "@types/node": "^18.7.18", "@types/qrcode": "^1.5.0", "@types/uniqid": "^5.3.2", "@types/webfontloader": "^1.6.34", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "@vitejs/plugin-vue": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/test-utils": "^2.4.6", "eslint": "^8.28.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.0.1", "eslint-plugin-sonarjs": "^0.16.0", "eslint-plugin-unicorn": "^45.0.0", "eslint-plugin-vue": "^9.5.1", "jest": "^29.7.0", "postcss-html": "^1.5.0", "prettier": "3.1.0", "stylelint": "^14.15.0", "stylelint-config-idiomatic-order": "^9.0.0", "stylelint-config-standard-scss": "^6.1.0", "stylelint-use-logical-spec": "^4.1.0", "ts-node": "^10.9.2", "type-fest": "^3.6.1", "typescript": "^4.9.5", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.24.1", "vite": "^4.4.9", "vite-plugin-pages": "^0.28.0", "vite-plugin-vue-devtools": "1.0.0-rc.5", "vite-plugin-vue-layouts": "^0.8.0", "vite-plugin-vuetify": "1.0.2", "vue-tsc": "^1.2.0"}, "packageManager": "yarn@1.22.18", "resolutions": {"postcss": "8", "stylelint-order": "5", "postcss-sorting": "^7.0.1"}}